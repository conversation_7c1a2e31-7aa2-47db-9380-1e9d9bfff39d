import { pchujoy } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { gameService } from "../../src/game";
import { gameSeasonService } from "../../src/game-season";
import { seasonService } from "../../src/season";
import { TestData, adminAuthToken } from "../common";

describe("create game season tests", () => {
  test("create game season", async () => {
    const game = await gameService.create(TestData.game);
    const season = await seasonService.create({
      name: "test",
      endDate: "2024-01-01",
      startDate: "2024-01-01",
    });
    await gameSeasonService.create({
      gameId: game.id,
      seasonId: season.id,
    });
    const query: pchujoy.gameSeason.DeleteGameSeasonRequest = {
      gameId: game.id,
      seasonId: season.id,
    };

    const response = await request(app)
      .delete("/live/game-season")
      .query(query)
      .set("Cookie", `session=${await adminAuthToken}`);

    const savedSeasons = await gameSeasonService.list({
      seasonId: season.id,
    });
    expect(response.status).toBe(204);
    expect(savedSeasons.length).toEqual(0);
  });
});
