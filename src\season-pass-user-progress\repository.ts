import { and, eq, InferInsertModel, InferSelectModel, sql } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { seasonPassUserProgressTable } from "../schema";
import { sc } from "../services";
import { transformNullToUndefined } from "../common";

export type SeasonPassUserProgressEntity = InferSelectModel<
  typeof seasonPassUserProgressTable
>;

class SeasonPassUserProgressRepository extends RepositoryBase<
  typeof seasonPassUserProgressTable,
  { userId: number; seasonPassId: number }
> {
  constructor() {
    super(seasonPassUserProgressTable);
  }
  async addExperience(
    userId: number,
    seasonPassId: number,
    experience: number,
    levelId: number,
  ): Promise<SeasonPassUserProgressEntity> {
    const db = await sc.getDB();
    const result = await db
      .update(this.table)
      .set({
        currentExp: sql`${this.table.currentExp} + ${experience}`,
        currentLevel: levelId,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(this.table.userId, userId),
          eq(this.table.seasonPassId, seasonPassId),
        ),
      )
      .returning();
    return result[0];
  }

  async getProgress(
    userId: number,
    seasonPassId: number,
  ): Promise<SeasonPassUserProgressEntity | undefined> {
    const db = await sc.getDB();
    const result = await db
      .select()
      .from(this.table)
      .where(
        and(
          eq(this.table.userId, userId),
          eq(this.table.seasonPassId, seasonPassId),
        ),
      );
    return result[0];
  }

  async updateProgress(userId: number, seasonPassId: number, data: Partial<InferInsertModel<typeof seasonPassUserProgressTable>>): Promise<SeasonPassUserProgressEntity> {
    const result = await (
      await sc.getDB()
    )
      // use event table just as a type mock
      .update(this.table)
      .set(data)
      .where(and(eq(this.table.userId, userId), eq(this.table.seasonPassId, seasonPassId)))
      .returning();
    return transformNullToUndefined(result[0]) as SeasonPassUserProgressEntity;
  }
}

export const seasonPassUserProgressRepository =
  new SeasonPassUserProgressRepository();
