import { pchujoy } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { seasonService } from "../../src/season";
import { TestData, adminAuthToken } from "../common";

describe("update season tests", () => {
  test("updates an existing season by id", async () => {
    await seasonService.create(TestData.season);

    const requestBody: pchujoy.season.UpdateSeasonRequest = {
      id: TestData.season.id,
      name: crypto.randomUUID(),
    };

    const response = await request(app)
      .put("/live/season")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    const season = await seasonService.get("id", TestData.season.id);
    expect(response.status).toBe(200);
    expect(season?.name).toEqual(requestBody.name);
  });
});
