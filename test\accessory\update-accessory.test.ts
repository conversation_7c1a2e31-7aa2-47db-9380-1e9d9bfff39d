import request from "supertest";
import { app } from "../../src/api-handler";
import { accessoryService } from "../../src/accessory";
import { TestData, adminAuthToken, userAuthToken } from "../common";

describe("update accessory tests", () => {
  let createdAccessory: any;

  beforeEach(async () => {
    // Create an accessory before each test
    createdAccessory = await accessoryService.create(TestData.accessoryFace);
  });

  test("updates accessory name successfully", async () => {
    const requestBody = {
      id: createdAccessory.id,
      name: "Updated Accessory Name",
    };

    const response = await request(app)
      .put("/live/accessory")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      id: createdAccessory.id,
      name: "Updated Accessory Name",
      type: TestData.accessoryFace.type,
      value: TestData.accessoryFace.value,
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    });

    // Verify the accessory was actually updated in the database
    const updatedAccessory = await accessoryService.get(
      "id",
      createdAccessory.id,
    );
    expect(updatedAccessory?.name).toBe("Updated Accessory Name");
  });

  test("updates accessory type successfully", async () => {
    const requestBody = {
      id: createdAccessory.id,
      type: "hat" as const,
    };

    const response = await request(app)
      .put("/live/accessory")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body.type).toBe("hat");
  });

  test("updates accessory value successfully", async () => {
    const requestBody = {
      id: createdAccessory.id,
      value: "https://example.com/updated-image.png",
    };

    const response = await request(app)
      .put("/live/accessory")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body.value).toBe("https://example.com/updated-image.png");
  });

  test("updates multiple fields at once", async () => {
    const requestBody = {
      id: createdAccessory.id,
      name: "Completely Updated Accessory",
      type: "background" as const,
      value: "https://example.com/new-background.png",
    };

    const response = await request(app)
      .put("/live/accessory")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      id: createdAccessory.id,
      name: "Completely Updated Accessory",
      type: "background",
      value: "https://example.com/new-background.png",
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    });
  });

  test("works with user authentication", async () => {
    const requestBody = {
      id: createdAccessory.id,
      name: "User Updated Name",
    };

    const response = await request(app)
      .put("/live/accessory")
      .send(requestBody)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body.name).toBe("User Updated Name");
  });

  test("returns 400 when id is missing", async () => {
    const requestBody = {
      name: "Updated Name",
    };

    const response = await request(app)
      .put("/live/accessory")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(400);
  });

  test("returns 400 when name is empty string", async () => {
    const requestBody = {
      id: createdAccessory.id,
      name: "",
    };

    const response = await request(app)
      .put("/live/accessory")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(400);
  });

  test("returns 400 when value is empty string", async () => {
    const requestBody = {
      id: createdAccessory.id,
      value: "",
    };

    const response = await request(app)
      .put("/live/accessory")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(400);
  });

  test("returns 400 when type is invalid", async () => {
    const requestBody = {
      id: createdAccessory.id,
      type: "invalid_type",
    };

    const response = await request(app)
      .put("/live/accessory")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(400);
  });

  test("returns 401 when no authentication token is provided", async () => {
    const requestBody = {
      id: createdAccessory.id,
      name: "Updated Name",
    };

    const response = await request(app)
      .put("/live/accessory")
      .send(requestBody);

    expect(response.status).toBe(401);
  });

  test("handles non-existent accessory gracefully", async () => {
    const requestBody = {
      id: 999999, // Non-existent accessory ID
      name: "Updated Name",
    };

    const response = await request(app)
      .put("/live/accessory")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    // The behavior depends on the service implementation
    expect(response.statusCode).toEqual(404);
  });
});
