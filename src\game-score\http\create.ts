import { Middleware } from "@koa/router";
import {
  AppError,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { seasonService } from "../../season";
import { gameScoreService } from "../service";

export const createGameScore: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  pchujoy.gameScore.CreateGameScoreResponse
> = async ({ request, response, state }) => {
  const body = pchujoy.gameScore.CreateGameScoreRequestSchema.parse(
    request.body,
  );
  const season = await seasonService.getCurrentSeason();

  if (!season) {
    throw new AppError({
      code: "NoActiveSeason",
      message: "There is no active season",
      logLevel: "INFO",
      statusCode: "NOT_FOUND",
    });
  }

  await gameScoreService.create({
    gameId: body.gameId,
    score: body.score,
    seasonId: season.id,
    userId: state.auth.id,
  });

  response.body = body;
};
