import request from "supertest";
import { app } from "../../src/api-handler";
import { seasonPassService } from "../../src/season-pass";
import { TestData } from "../common";

describe("get current season pass tests", () => {
  beforeEach(() => {
    // Clear cache before each test (database is automatically rolled back)
    seasonPassService.clearCache();
  });

  test("gets the current active season pass", async () => {
    await seasonPassService.create(TestData.seasonPass);

    const response = await request(app).get("/live/season-pass/current");

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      id: expect.any(Number),
      name: TestData.seasonPass.name,
      bannerUrl: TestData.seasonPass.bannerUrl,
      startDate: TestData.seasonPass.startDate,
      endDate: TestData.seasonPass.endDate,
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    });
  });

  test("returns 404 when no active season pass exists", async () => {
    // Create a season pass that has already ended
    const expiredSeasonPass = {
      name: "Expired Season Pass",
      bannerUrl: "https://example.com/expired-banner.png",
      startDate: "2020-01-01",
      endDate: "2020-12-31",
    };

    await seasonPassService.create(expiredSeasonPass);

    const response = await request(app).get("/live/season-pass/current");

    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      code: "NoActiveSeasonPass",
      message: "There is no active season pass",
    });
  });

  test("returns cached season pass on subsequent requests", async () => {
    await seasonPassService.create(TestData.seasonPass);

    // First request
    const response1 = await request(app).get("/live/season-pass/current");
    expect(response1.status).toBe(200);

    // Second request should return the same data (cached)
    const response2 = await request(app).get("/live/season-pass/current");
    expect(response2.status).toBe(200);
    expect(response2.body).toEqual(response1.body);
  });
});
