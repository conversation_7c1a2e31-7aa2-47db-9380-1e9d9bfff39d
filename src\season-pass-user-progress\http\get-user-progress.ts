import { Middleware } from "@koa/router";
import {
  AppError,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { seasonPassService } from "../../season-pass";
import { seasonPassUserProgressService } from "../service";
import { seasonPassLevelService } from "../../season-pass-level";

export const getUserProgress: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  pchujoy.seasonPass.GetSeasonPassUserProgressResponse
> = async ({ response, state }) => {
  const currentSeasonPass = await seasonPassService.getCurrentSeasonPass();

  const progress = await seasonPassUserProgressService.getUserProgress(
    state.auth.id,
    currentSeasonPass.id,
  );

  if (!progress) {
    throw new AppError({
      code: "UserProgressNotFound",
      message:
        "No se encontró el progreso del usuario para el season pass actual",
      statusCode: "NOT_FOUND",
    });
  }

  const level = await seasonPassLevelService.get("id", progress.currentLevel);

  response.body = {
    currentExp: progress.currentExp,
    currentLevel: {
      name: level?.name || "",
      description: level?.description || "",
    },
  };
};
