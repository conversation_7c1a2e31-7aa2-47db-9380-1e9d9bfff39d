import { Middleware } from "@koa/router";
import {
  AppError,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { seasonService } from "../../season";
import { gameScoreService } from "../service";

export const getUserScores: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  pchujoy.gameScore.GetUserScoresResponse
> = async ({ request, response, state }) => {
  const query = pchujoy.gameScore.GetUserScoresRequestSchema.parse(
    request.query,
  );

  const season = await seasonService.getCurrentSeason();
  if (!season) {
    throw new AppError({
      code: "NoActiveSeason",
      message: "There is no active season",
      logLevel: "INFO",
      statusCode: "NOT_FOUND",
    });
  }

  const scores = await gameScoreService.getUserScoresByGame({
    gameId: query.gameId,
    seasonId: season.id,
    userId: state.auth.id,
  });

  response.set("Cache-Control", "s-maxage=1800");
  response.body = scores.map((i) => ({
    rank: i.rank,
    date: i.date.toISOString(),
    score: i.score,
  }));
};
