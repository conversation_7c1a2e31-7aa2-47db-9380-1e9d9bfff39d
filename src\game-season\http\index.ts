import Router from "@koa/router";
import { getClientAuthMiddleware } from "@mainframe-peru/common-core";
import { pchujoyMiddleware } from "../../common";
import { sc } from "../../services";
import { createGameSeason } from "./create";
import { deleteGameSeason } from "./delete";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

// Game season
router.post("/", authMiddleware, pchujoyMiddleware, createGameSeason);
router.delete("/", authMiddleware, pchujoyMiddleware, deleteGameSeason);

export const gameSeasonRouter = router;
