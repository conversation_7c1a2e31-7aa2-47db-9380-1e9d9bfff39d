import Router from "@koa/router";
import { getClientAuthMiddleware } from "@mainframe-peru/common-core";
import { pchujoyMiddleware } from "../../common";
import { sc } from "../../services";
import { createSeason } from "./create-season";
import { deleteSeason } from "./delete-season";
import { getSeason } from "./get-season";
import { listSeason } from "./list-season";
import { updateSeason } from "./update-season";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

// Season
router.post("/", authMiddleware, pchujoyMiddleware, createSeason);
router.put("/", authMiddleware, pchujoyMiddleware, updateSeason);
router.delete("/", authMiddleware, pchujoyMiddleware, deleteSeason);

/**
 * Endpoints without authentication
 */
router.get("/", getSeason);
router.get("/list", listSeason);

export const seasonRouter = router;
