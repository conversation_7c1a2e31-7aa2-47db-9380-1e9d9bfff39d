CREATE TYPE "pchujoy"."mission_action" AS ENUM('CREATE_ACCOUNT', 'LOGI<PERSON>', 'SIGNIN_GOOGLE', 'LOGOUT', 'UPDATE_ACCOUNT', 'UPDATE_PASSWORD', 'RESET_PASSWORD', 'DELETE_ACCOUNT', 'CREATE_RECURRENCE', 'UPDATE_RECURRENCE', 'CREATE_PAYMENT', 'EXT_CREATE_USER', 'EXT_CREATE_CARD', 'EXT_CREATE_CHARGE', 'CREATE_TRANSACTION', 'EVENT_PARTICIPATION', 'CREATE_EVENT', 'UPDATE_EVENT', 'DELETE_EVENT');--> statement-breakpoint
ALTER TABLE "pchujoy"."season_pass_mission" ALTER COLUMN "action" SET DATA TYPE "pchujoy"."mission_action" USING "action"::text::"pchujoy"."mission_action";
ALTER TABLE "pchujoy"."profile" DROP COLUMN "nickname";--> statement-breakpoint
ALTER TABLE "pchujoy"."profile" DROP COLUMN "discriminator";