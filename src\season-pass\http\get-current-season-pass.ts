import { Middleware } from "@koa/router";
import { seasonPassService } from "../service";
import { SeasonPassEntity } from "../repository";

export const getCurrentSeasonPass: Middleware<
  unknown,
  unknown,
  SeasonPassEntity
> = async ({ response }) => {
  const entity = await seasonPassService.getCurrentSeasonPass();

  response.set("Cache-Control", "s-maxage=1800");
  response.body = entity;
};
