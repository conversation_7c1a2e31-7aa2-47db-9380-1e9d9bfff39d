import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { seasonService } from "../service";

export const updateSeason: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  pchujoy.season.UpdateSeasonResponse
> = async ({ request, response }) => {
  const { id, ...update } = pchujoy.season.UpdateSeasonRequestSchema.parse(
    request.body,
  );
  const updatedSeason = await seasonService.update(id, update);

  response.body =
    pchujoy.season.UpdateSeasonResponseSchema.parse(updatedSeason);
};
