import { pchujoy } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { gameService } from "../../src/game";
import { TestData, adminAuthToken } from "../common";

describe("create game tests", () => {
  test("create a game", async () => {
    const requestBody: pchujoy.game.CreateGameRequest = TestData.game;

    const response = await request(app)
      .post("/live/game")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    const game = await gameService.get("id", TestData.game.id);
    expect(response.status).toBe(200);
    expect(game).toEqual({
      ...TestData.game,
      createdAt: expect.any(Date),
      updatedAt: expect.any(Date),
    });
  });
});
