import Router from "@koa/router";
import { getClientAuthMiddleware } from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { getUserInventory } from "./get-user-inventory";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

// Inventory
router.get("/", authMiddleware, getUserInventory);

export const inventoryRouter = router;
