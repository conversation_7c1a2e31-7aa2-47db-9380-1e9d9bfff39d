CREATE TYPE "pchujoy"."accessory_type" AS ENUM('face', 'hat', 'pet', 'glasses', 'title', 'background');--> statement-breakpoint
CREATE TYPE "pchujoy"."mission_period" AS ENUM('daily', 'weekly');--> statement-breakpoint
CREATE TYPE "pchujoy"."mission_type" AS ENUM('regular', 'boost');--> statement-breakpoint
CREATE TABLE "pchujoy"."accessory" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"type" "pchujoy"."accessory_type" NOT NULL,
	"value" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "pchujoy"."inventory" (
	"accessory_id" integer NOT NULL,
	"user_id" integer NOT NULL,
	"redeemed_date" timestamp DEFAULT now() NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "inventory_accessory_id_user_id_pk" PRIMARY KEY("accessory_id","user_id")
);
--> statement-breakpoint
CREATE TABLE "pchujoy"."profile" (
	"user_id" integer PRIMARY KEY NOT NULL,
	"nickname" text NOT NULL,
	"discriminator" text NOT NULL,
	"background_id" integer,
	"face_id" integer,
	"hat_id" integer,
	"pet_id" integer,
	"glasses_id" integer,
	"title_id" integer,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "pchujoy"."season_pass_level" (
	"id" serial PRIMARY KEY NOT NULL,
	"season_pass_id" integer NOT NULL,
	"level_index" integer NOT NULL,
	"name" text NOT NULL,
	"description" text NOT NULL,
	"rewards" jsonb NOT NULL,
	"level_exp" integer NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "pchujoy"."season_pass_mission" (
	"id" serial PRIMARY KEY NOT NULL,
	"season_pass_id" integer NOT NULL,
	"name" text NOT NULL,
	"description" text NOT NULL,
	"experience" integer NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"type" "pchujoy"."mission_type" NOT NULL,
	"start_at" timestamp NOT NULL,
	"end_at" timestamp NOT NULL,
	"period" "pchujoy"."mission_period" NOT NULL,
	"action" text NOT NULL,
	"action_count" integer DEFAULT 1 NOT NULL,
	"sort_order" integer DEFAULT 0 NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "pchujoy"."season_pass_mission_user" (
	"user_id" integer NOT NULL,
	"season_pass_id" integer NOT NULL,
	"season_pass_mission_id" integer NOT NULL,
	"progress_count" integer DEFAULT 1 NOT NULL,
	"completed" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "season_pass_mission_user_user_id_season_pass_mission_id_pk" PRIMARY KEY("user_id","season_pass_mission_id")
);
--> statement-breakpoint
CREATE TABLE "pchujoy"."season_pass" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"banner_url" text NOT NULL,
	"start_date" date NOT NULL,
	"end_date" date NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "pchujoy"."season_pass_user_progress" (
	"user_id" integer NOT NULL,
	"season_pass_id" integer NOT NULL,
	"current_exp" integer DEFAULT 0 NOT NULL,
	"current_level" integer NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "season_pass_user_progress_user_id_season_pass_id_pk" PRIMARY KEY("user_id","season_pass_id")
);
--> statement-breakpoint
ALTER TABLE "pchujoy"."inventory" ADD CONSTRAINT "inventory_accessory_id_accessory_id_fk" FOREIGN KEY ("accessory_id") REFERENCES "pchujoy"."accessory"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."inventory" ADD CONSTRAINT "inventory_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "core_backend"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."profile" ADD CONSTRAINT "profile_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "core_backend"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."profile" ADD CONSTRAINT "profile_background_id_accessory_id_fk" FOREIGN KEY ("background_id") REFERENCES "pchujoy"."accessory"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."profile" ADD CONSTRAINT "profile_face_id_accessory_id_fk" FOREIGN KEY ("face_id") REFERENCES "pchujoy"."accessory"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."profile" ADD CONSTRAINT "profile_hat_id_accessory_id_fk" FOREIGN KEY ("hat_id") REFERENCES "pchujoy"."accessory"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."profile" ADD CONSTRAINT "profile_pet_id_accessory_id_fk" FOREIGN KEY ("pet_id") REFERENCES "pchujoy"."accessory"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."profile" ADD CONSTRAINT "profile_glasses_id_accessory_id_fk" FOREIGN KEY ("glasses_id") REFERENCES "pchujoy"."accessory"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."profile" ADD CONSTRAINT "profile_title_id_accessory_id_fk" FOREIGN KEY ("title_id") REFERENCES "pchujoy"."accessory"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."season_pass_level" ADD CONSTRAINT "season_pass_level_season_pass_id_season_pass_id_fk" FOREIGN KEY ("season_pass_id") REFERENCES "pchujoy"."season_pass"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."season_pass_mission" ADD CONSTRAINT "season_pass_mission_season_pass_id_season_pass_id_fk" FOREIGN KEY ("season_pass_id") REFERENCES "pchujoy"."season_pass"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."season_pass_mission_user" ADD CONSTRAINT "season_pass_mission_user_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "core_backend"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."season_pass_mission_user" ADD CONSTRAINT "season_pass_mission_user_season_pass_id_season_pass_id_fk" FOREIGN KEY ("season_pass_id") REFERENCES "pchujoy"."season_pass"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."season_pass_mission_user" ADD CONSTRAINT "season_pass_mission_user_season_pass_mission_id_season_pass_mission_id_fk" FOREIGN KEY ("season_pass_mission_id") REFERENCES "pchujoy"."season_pass_mission"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."season_pass_user_progress" ADD CONSTRAINT "season_pass_user_progress_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "core_backend"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."season_pass_user_progress" ADD CONSTRAINT "season_pass_user_progress_season_pass_id_season_pass_id_fk" FOREIGN KEY ("season_pass_id") REFERENCES "pchujoy"."season_pass"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."season_pass_user_progress" ADD CONSTRAINT "season_pass_user_progress_current_level_season_pass_level_id_fk" FOREIGN KEY ("current_level") REFERENCES "pchujoy"."season_pass_level"("id") ON DELETE no action ON UPDATE no action;