import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { gameService } from "../service";

export const createGame: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  pchujoy.game.CreateGameResponse
> = async ({ request, response }) => {
  const body = pchujoy.game.CreateGameRequestSchema.parse(request.body);
  const game = await gameService.create(body);

  response.body = {
    id: game.id,
    createdAt: game.createdAt,
    description: game.description,
    imageUrl: game.imageUrl,
    name: game.name,
    sourceUrl: game.sourceUrl,
    status: game.status,
    updatedAt: game.updatedAt,
  };
};
