{"id": "990308c9-cca6-4254-8d2d-ddcd62d2ad41", "prevId": "7b473f71-888a-42b2-b472-ec82e15b0cbf", "version": "7", "dialect": "postgresql", "tables": {"pchujoy.accessory": {"name": "accessory", "schema": "p<PERSON><PERSON>", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "accessory_type", "typeSchema": "p<PERSON><PERSON>", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "pchujoy.game_score": {"name": "game_score", "schema": "p<PERSON><PERSON>", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "season_id": {"name": "season_id", "type": "integer", "primaryKey": false, "notNull": true}, "game_id": {"name": "game_id", "type": "text", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"game_score_user_id_user_id_fk": {"name": "game_score_user_id_user_id_fk", "tableFrom": "game_score", "tableTo": "user", "schemaTo": "core_backend", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "game_score_season_id_season_id_fk": {"name": "game_score_season_id_season_id_fk", "tableFrom": "game_score", "tableTo": "season", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["season_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "game_score_game_id_game_id_fk": {"name": "game_score_game_id_game_id_fk", "tableFrom": "game_score", "tableTo": "game", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["game_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "pchujoy.game_season": {"name": "game_season", "schema": "p<PERSON><PERSON>", "columns": {"season_id": {"name": "season_id", "type": "integer", "primaryKey": false, "notNull": true}, "game_id": {"name": "game_id", "type": "text", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"game_season_season_id_season_id_fk": {"name": "game_season_season_id_season_id_fk", "tableFrom": "game_season", "tableTo": "season", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["season_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "game_season_game_id_game_id_fk": {"name": "game_season_game_id_game_id_fk", "tableFrom": "game_season", "tableTo": "game", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["game_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"game_season_season_id_game_id_pk": {"name": "game_season_season_id_game_id_pk", "columns": ["season_id", "game_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "pchujoy.game": {"name": "game", "schema": "p<PERSON><PERSON>", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true}, "source_url": {"name": "source_url", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "game_status", "typeSchema": "p<PERSON><PERSON>", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "pchujoy.inventory": {"name": "inventory", "schema": "p<PERSON><PERSON>", "columns": {"accessory_id": {"name": "accessory_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "redeemed_date": {"name": "redeemed_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"inventory_accessory_id_accessory_id_fk": {"name": "inventory_accessory_id_accessory_id_fk", "tableFrom": "inventory", "tableTo": "accessory", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["accessory_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "inventory_user_id_user_id_fk": {"name": "inventory_user_id_user_id_fk", "tableFrom": "inventory", "tableTo": "user", "schemaTo": "core_backend", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"inventory_accessory_id_user_id_pk": {"name": "inventory_accessory_id_user_id_pk", "columns": ["accessory_id", "user_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "pchujoy.profile": {"name": "profile", "schema": "p<PERSON><PERSON>", "columns": {"user_id": {"name": "user_id", "type": "integer", "primaryKey": true, "notNull": true}, "nickname": {"name": "nickname", "type": "text", "primaryKey": false, "notNull": true}, "discriminator": {"name": "discriminator", "type": "text", "primaryKey": false, "notNull": true}, "background_id": {"name": "background_id", "type": "integer", "primaryKey": false, "notNull": false}, "skin_id": {"name": "skin_id", "type": "integer", "primaryKey": false, "notNull": false}, "face_id": {"name": "face_id", "type": "integer", "primaryKey": false, "notNull": false}, "hair_id": {"name": "hair_id", "type": "integer", "primaryKey": false, "notNull": false}, "hat_id": {"name": "hat_id", "type": "integer", "primaryKey": false, "notNull": false}, "pet_id": {"name": "pet_id", "type": "integer", "primaryKey": false, "notNull": false}, "glasses_id": {"name": "glasses_id", "type": "integer", "primaryKey": false, "notNull": false}, "title_id": {"name": "title_id", "type": "integer", "primaryKey": false, "notNull": false}, "top_id": {"name": "top_id", "type": "integer", "primaryKey": false, "notNull": false}, "pet_bg_color": {"name": "pet_bg_color", "type": "text", "primaryKey": false, "notNull": false}, "avatar_bg_color": {"name": "avatar_bg_color", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"profile_user_id_user_id_fk": {"name": "profile_user_id_user_id_fk", "tableFrom": "profile", "tableTo": "user", "schemaTo": "core_backend", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "profile_background_id_accessory_id_fk": {"name": "profile_background_id_accessory_id_fk", "tableFrom": "profile", "tableTo": "accessory", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["background_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "profile_skin_id_accessory_id_fk": {"name": "profile_skin_id_accessory_id_fk", "tableFrom": "profile", "tableTo": "accessory", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["skin_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "profile_face_id_accessory_id_fk": {"name": "profile_face_id_accessory_id_fk", "tableFrom": "profile", "tableTo": "accessory", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["face_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "profile_hair_id_accessory_id_fk": {"name": "profile_hair_id_accessory_id_fk", "tableFrom": "profile", "tableTo": "accessory", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["hair_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "profile_hat_id_accessory_id_fk": {"name": "profile_hat_id_accessory_id_fk", "tableFrom": "profile", "tableTo": "accessory", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["hat_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "profile_pet_id_accessory_id_fk": {"name": "profile_pet_id_accessory_id_fk", "tableFrom": "profile", "tableTo": "accessory", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["pet_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "profile_glasses_id_accessory_id_fk": {"name": "profile_glasses_id_accessory_id_fk", "tableFrom": "profile", "tableTo": "accessory", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["glasses_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "profile_title_id_accessory_id_fk": {"name": "profile_title_id_accessory_id_fk", "tableFrom": "profile", "tableTo": "accessory", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["title_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "profile_top_id_accessory_id_fk": {"name": "profile_top_id_accessory_id_fk", "tableFrom": "profile", "tableTo": "accessory", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["top_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "pchujoy.season_pass_level": {"name": "season_pass_level", "schema": "p<PERSON><PERSON>", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "season_pass_id": {"name": "season_pass_id", "type": "integer", "primaryKey": false, "notNull": true}, "level_index": {"name": "level_index", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "rewards": {"name": "rewards", "type": "jsonb", "primaryKey": false, "notNull": true}, "level_exp": {"name": "level_exp", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"season_pass_level_season_pass_id_season_pass_id_fk": {"name": "season_pass_level_season_pass_id_season_pass_id_fk", "tableFrom": "season_pass_level", "tableTo": "season_pass", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["season_pass_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "pchujoy.season_pass_mission": {"name": "season_pass_mission", "schema": "p<PERSON><PERSON>", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "season_pass_id": {"name": "season_pass_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "experience": {"name": "experience", "type": "integer", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "type": {"name": "type", "type": "mission_type", "typeSchema": "p<PERSON><PERSON>", "primaryKey": false, "notNull": true}, "start_at": {"name": "start_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_at": {"name": "end_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "period": {"name": "period", "type": "mission_period", "typeSchema": "p<PERSON><PERSON>", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true}, "action_count": {"name": "action_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"season_pass_mission_season_pass_id_season_pass_id_fk": {"name": "season_pass_mission_season_pass_id_season_pass_id_fk", "tableFrom": "season_pass_mission", "tableTo": "season_pass", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["season_pass_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "pchujoy.season_pass_mission_user": {"name": "season_pass_mission_user", "schema": "p<PERSON><PERSON>", "columns": {"user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "season_pass_id": {"name": "season_pass_id", "type": "integer", "primaryKey": false, "notNull": true}, "season_pass_mission_id": {"name": "season_pass_mission_id", "type": "integer", "primaryKey": false, "notNull": true}, "progress_count": {"name": "progress_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "completed": {"name": "completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"season_pass_mission_user_user_id_user_id_fk": {"name": "season_pass_mission_user_user_id_user_id_fk", "tableFrom": "season_pass_mission_user", "tableTo": "user", "schemaTo": "core_backend", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "season_pass_mission_user_season_pass_id_season_pass_id_fk": {"name": "season_pass_mission_user_season_pass_id_season_pass_id_fk", "tableFrom": "season_pass_mission_user", "tableTo": "season_pass", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["season_pass_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "season_pass_mission_user_season_pass_mission_id_season_pass_mission_id_fk": {"name": "season_pass_mission_user_season_pass_mission_id_season_pass_mission_id_fk", "tableFrom": "season_pass_mission_user", "tableTo": "season_pass_mission", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["season_pass_mission_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"season_pass_mission_user_user_id_season_pass_mission_id_pk": {"name": "season_pass_mission_user_user_id_season_pass_mission_id_pk", "columns": ["user_id", "season_pass_mission_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "pchujoy.season_pass": {"name": "season_pass", "schema": "p<PERSON><PERSON>", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "banner_url": {"name": "banner_url", "type": "text", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "pchujoy.season_pass_user_progress": {"name": "season_pass_user_progress", "schema": "p<PERSON><PERSON>", "columns": {"user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "season_pass_id": {"name": "season_pass_id", "type": "integer", "primaryKey": false, "notNull": true}, "current_exp": {"name": "current_exp", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "current_level": {"name": "current_level", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"season_pass_user_progress_user_id_user_id_fk": {"name": "season_pass_user_progress_user_id_user_id_fk", "tableFrom": "season_pass_user_progress", "tableTo": "user", "schemaTo": "core_backend", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "season_pass_user_progress_season_pass_id_season_pass_id_fk": {"name": "season_pass_user_progress_season_pass_id_season_pass_id_fk", "tableFrom": "season_pass_user_progress", "tableTo": "season_pass", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["season_pass_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "season_pass_user_progress_current_level_season_pass_level_id_fk": {"name": "season_pass_user_progress_current_level_season_pass_level_id_fk", "tableFrom": "season_pass_user_progress", "tableTo": "season_pass_level", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["current_level"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"season_pass_user_progress_user_id_season_pass_id_pk": {"name": "season_pass_user_progress_user_id_season_pass_id_pk", "columns": ["user_id", "season_pass_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "pchujoy.season": {"name": "season", "schema": "p<PERSON><PERSON>", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"pchujoy.accessory_type": {"name": "accessory_type", "schema": "p<PERSON><PERSON>", "values": ["face", "hat", "pet", "glasses", "title", "background", "top", "hair", "skin"]}, "pchujoy.game_status": {"name": "game_status", "schema": "p<PERSON><PERSON>", "values": ["ACTIVE", "INACTIVE"]}, "pchujoy.mission_period": {"name": "mission_period", "schema": "p<PERSON><PERSON>", "values": ["daily", "weekly"]}, "pchujoy.mission_type": {"name": "mission_type", "schema": "p<PERSON><PERSON>", "values": ["regular", "boost"]}}, "schemas": {"pchujoy": "p<PERSON><PERSON>"}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}