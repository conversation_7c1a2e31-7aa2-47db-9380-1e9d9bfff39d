import { pchujoy } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { gameService } from "../../src/game";
import { TestData } from "../common";

describe("get game tests", () => {
  test("gets an existing game by id", async () => {
    await gameService.create(TestData.game);

    const queryParams: pchujoy.game.GetGameRequest = {
      id: TestData.game.id,
    };

    const response = await request(app).get("/live/game").query(queryParams);

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      description: TestData.game.description,
      id: TestData.game.id,
      imageUrl: TestData.game.imageUrl,
      name: TestData.game.name,
      sourceUrl: TestData.game.sourceUrl,
      status: TestData.game.status,
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    });
  });
});
