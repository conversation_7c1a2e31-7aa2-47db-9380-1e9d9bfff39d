import { userEventParticipation, webSockets } from "@mainframe-peru/types";
import { sc } from "../services";
import { PublishCommand, PublishCommandOutput } from "@aws-sdk/client-sns";
import { logger } from "@mainframe-peru/common-core";

export const coreBackendQueue = {
  async sendAddUserEventParticipationMessage(
    request: userEventParticipation.CreateUserEventParticipationRequest,
  ): Promise<PublishCommandOutput | undefined> {
    try {
      const command = new PublishCommand({
        TopicArn: sc.vars.snsTopicArn,
        Message: JSON.stringify(request),
        MessageAttributes: {
          service: {
            DataType: "String",
            StringValue: "core-backend",
          },
        },
      });
      return await sc.sns.send(command);
    } catch (e) {
      logger.error("Error sending message to core-backend queue", e);
    }
  },
};
