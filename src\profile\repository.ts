import { eq, InferSelectModel } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { accessoryTable, profileTable } from "../schema";
import { sc } from "../services";
import { alias } from "drizzle-orm/pg-core";

export type ProfileEntity = InferSelectModel<typeof profileTable>;

class ProfileRepository extends RepositoryBase<typeof profileTable, number> {
  constructor() {
    super(profileTable);
  }

  get tableId() {
    return this.table.userId;
  }

  async getFullProfile(userId: number) {
    const db = await sc.getDB();

    const background = alias(accessoryTable, "background");
    const skin = alias(accessoryTable, "skin");
    const top = alias(accessoryTable, "top");
    const face = alias(accessoryTable, "face");
    const hair = alias(accessoryTable, "hair");
    const hat = alias(accessoryTable, "hat");
    const glasses = alias(accessoryTable, "glasses");
    const title = alias(accessoryTable, "title");
    const pet = alias(accessoryTable, "pet");

    const [targetProfile] = await db
      .select()
      .from(this.table)
      .where(eq(this.table.userId, userId))
      .leftJoin(background, eq(this.table.backgroundId, background.id))
      .leftJoin(skin, eq(this.table.skinId, skin.id))
      .leftJoin(top, eq(this.table.topId, top.id))
      .leftJoin(face, eq(this.table.faceId, face.id))
      .leftJoin(hair, eq(this.table.hairId, hair.id))
      .leftJoin(hat, eq(this.table.hatId, hat.id))
      .leftJoin(glasses, eq(this.table.glassesId, glasses.id))
      .leftJoin(title, eq(this.table.titleId, title.id))
      .leftJoin(pet, eq(this.table.petId, pet.id));

    return targetProfile;
  }
}

export const profileRepository = new ProfileRepository();
