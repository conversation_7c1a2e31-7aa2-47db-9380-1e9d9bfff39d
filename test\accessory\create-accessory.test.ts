import request from "supertest";
import { app } from "../../src/api-handler";
import { accessoryService } from "../../src/accessory";
import { TestData, adminAuthToken, userAuthToken } from "../common";

describe("create accessory tests", () => {
  test("creates a new accessory successfully", async () => {
    const requestBody = {
      name: TestData.accessoryFace.name,
      type: TestData.accessoryFace.type,
      value: TestData.accessoryFace.value,
    };

    const response = await request(app)
      .post("/live/accessory")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      id: expect.any(Number),
      name: TestData.accessoryFace.name,
      type: TestData.accessoryFace.type,
      value: TestData.accessoryFace.value,
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    });

    // Verify the accessory was actually created in the database
    const createdAccessory = await accessoryService.get("id", response.body.id);
    expect(createdAccessory).toBeDefined();
    expect(createdAccessory?.name).toBe(TestData.accessoryFace.name);
  });

  test("creates accessories with different types", async () => {
    const accessoryTypes = [
      "face",
      "hat",
      "pet",
      "glasses",
      "title",
      "background",
    ] as const;

    for (const type of accessoryTypes) {
      const requestBody = {
        name: `Test ${type}`,
        type: type,
        value: `https://example.com/${type}.png`,
      };

      const response = await request(app)
        .post("/live/accessory")
        .send(requestBody)
        .set("Cookie", `session=${await adminAuthToken}`);

      expect(response.status).toBe(200);
      expect(response.body.type).toBe(type);
    }
  });

  test("works with user authentication", async () => {
    const requestBody = {
      name: "User Created Accessory",
      type: "hat" as const,
      value: "https://example.com/user-hat.png",
    };

    const response = await request(app)
      .post("/live/accessory")
      .send(requestBody)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body.name).toBe("User Created Accessory");
  });

  test("returns 400 when name is empty", async () => {
    const requestBody = {
      name: "",
      type: "glasses" as const,
      value: "https://example.com/glasses.png",
    };

    const response = await request(app)
      .post("/live/accessory")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(400);
  });

  test("returns 400 when value is empty", async () => {
    const requestBody = {
      name: "Test Accessory",
      type: "glasses" as const,
      value: "",
    };

    const response = await request(app)
      .post("/live/accessory")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(400);
  });

  test("returns 400 when type is invalid", async () => {
    const requestBody = {
      name: "Test Accessory",
      type: "invalid_type",
      value: "https://example.com/test.png",
    };

    const response = await request(app)
      .post("/live/accessory")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(400);
  });

  test("returns 400 when required fields are missing", async () => {
    const incompleteBody = {
      name: "Test Accessory",
      // Missing type and value
    };

    const response = await request(app)
      .post("/live/accessory")
      .send(incompleteBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(400);
  });

  test("returns 401 when no authentication token is provided", async () => {
    const requestBody = {
      name: TestData.accessoryFace.name,
      type: TestData.accessoryFace.type,
      value: TestData.accessoryFace.value,
    };

    const response = await request(app)
      .post("/live/accessory")
      .send(requestBody);

    expect(response.status).toBe(401);
  });
});
