import { pchujoy } from "@mainframe-peru/types";
import {
  date,
  integer,
  pgSchema,
  primaryKey,
  serial,
  text,
  timestamp,
  jsonb,
  boolean,
} from "drizzle-orm/pg-core";
import { userTable } from "./core-schema";
import { ActivityLogEventsEnum } from "@mainframe-peru/types/build/common";

export const pchujoyBackendSchema = pgSchema("pchujoy");

export const gamesStatusEnum = pchujoyBackendSchema.enum(
  "game_status",
  pchujoy.Enums.GameStatus.options,
);

export const accessoryTypeEnum = pchujoyBackendSchema.enum(
  "accessory_type",
  pchujoy.Enums.AccessoryType.options,
);

export const missionTypeEnum = pchujoyBackendSchema.enum(
  "mission_type",
  pchujoy.Enums.MissionType.options,
);

export const missionPeriodEnum = pchujoyBackendSchema.enum(
  "mission_period",
  pchujoy.Enums.MissionPeriod.options,
);

export const missionActionEnum = pchujoyBackendSchema.enum(
  "mission_action",
  ActivityLogEventsEnum.options,
);

export const gameTable = pchujoyBackendSchema.table("game", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description").notNull(),
  imageUrl: text("image_url").notNull(),
  sourceUrl: text("source_url").notNull(),
  status: gamesStatusEnum("status").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const seasonTable = pchujoyBackendSchema.table("season", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  startDate: date("start_date").notNull(),
  endDate: date("end_date").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const gameSeasonTable = pchujoyBackendSchema.table(
  "game_season",
  {
    seasonId: integer("season_id")
      .references(() => seasonTable.id)
      .notNull(),
    gameId: text("game_id")
      .references(() => gameTable.id)
      .notNull(),
    priority: integer("priority"),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at").notNull().defaultNow(),
  },
  (table) => [primaryKey({ columns: [table.seasonId, table.gameId] })],
);

export const gameScoreTable = pchujoyBackendSchema.table("game_score", {
  id: serial("id").primaryKey(),
  userId: integer("user_id")
    .references(() => userTable.id)
    .notNull(),
  seasonId: integer("season_id")
    .references(() => seasonTable.id)
    .notNull(),
  gameId: text("game_id")
    .references(() => gameTable.id)
    .notNull(),
  score: integer("score").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Profile table for user profile information
export const profileTable = pchujoyBackendSchema.table("profile", {
  userId: integer("user_id")
    .references(() => userTable.id)
    .primaryKey(),
  // avatar related
  backgroundId: integer("background_id").references(() => accessoryTable.id),
  skinId: integer("skin_id").references(() => accessoryTable.id),
  faceId: integer("face_id").references(() => accessoryTable.id),
  hairId: integer("hair_id").references(() => accessoryTable.id),
  hatId: integer("hat_id").references(() => accessoryTable.id),
  petId: integer("pet_id").references(() => accessoryTable.id),
  glassesId: integer("glasses_id").references(() => accessoryTable.id),
  titleId: integer("title_id").references(() => accessoryTable.id),
  topId: integer("top_id").references(() => accessoryTable.id),
  petBgColor: text("pet_bg_color"),
  avatarBgColor: text("avatar_bg_color"),
  // end avatar related
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Accessory table for game accessories/items
export const accessoryTable = pchujoyBackendSchema.table("accessory", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  type: accessoryTypeEnum("type").notNull(),
  value: text("value").notNull(), // URL or text value
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Inventory table for user's accessory inventory
export const inventoryTable = pchujoyBackendSchema.table(
  "inventory",
  {
    accessoryId: integer("accessory_id")
      .references(() => accessoryTable.id)
      .notNull(),
    userId: integer("user_id")
      .references(() => userTable.id)
      .notNull(),
    redeemedDate: timestamp("redeemed_date").notNull().defaultNow(),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at").notNull().defaultNow(),
  },
  (table) => [primaryKey({ columns: [table.accessoryId, table.userId] })],
);

// Season Pass table
export const seasonPassTable = pchujoyBackendSchema.table("season_pass", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  bannerUrl: text("banner_url").notNull(),
  startDate: date("start_date").notNull(),
  endDate: date("end_date").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Season Pass Level table
export const seasonPassLevelTable = pchujoyBackendSchema.table(
  "season_pass_level",
  {
    id: serial("id").primaryKey(),
    seasonPassId: integer("season_pass_id")
      .references(() => seasonPassTable.id)
      .notNull(),
    levelIndex: integer("level_index").notNull(),
    name: text("name").notNull(),
    description: text("description").notNull(),
    rewards: jsonb("rewards").$type<pchujoy.seasonPass.Reward[]>().notNull(), // Array of reward objects with type, id, etc.
    levelExp: integer("level_exp").notNull(),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at").notNull().defaultNow(),
  },
);

// Season Pass User Progress table
export const seasonPassUserProgressTable = pchujoyBackendSchema.table(
  "season_pass_user_progress",
  {
    userId: integer("user_id")
      .references(() => userTable.id)
      .notNull(),
    seasonPassId: integer("season_pass_id")
      .references(() => seasonPassTable.id)
      .notNull(),
    currentExp: integer("current_exp").notNull().default(0),
    currentLevel: integer("current_level")
      .references(() => seasonPassLevelTable.id)
      .notNull(),
    levelsClaimed: integer("levels_claimed").array().notNull().default([]),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at").notNull().defaultNow(),
  },
  (table) => [primaryKey({ columns: [table.userId, table.seasonPassId] })],
);

// Season Pass Mission table
export const seasonPassMissionTable = pchujoyBackendSchema.table(
  "season_pass_mission",
  {
    id: serial("id").primaryKey(),
    seasonPassId: integer("season_pass_id")
      .references(() => seasonPassTable.id)
      .notNull(),
    name: text("name").notNull(),
    description: text("description").notNull(),
    experience: integer("experience").notNull(),
    isActive: boolean("is_active").notNull().default(true),
    type: missionTypeEnum("type").notNull(),
    startAt: timestamp("start_at").notNull(),
    endAt: timestamp("end_at").notNull(),
    period: missionPeriodEnum("period").notNull(),
    action: missionActionEnum("action").notNull(),
    actionCount: integer("action_count").notNull().default(1),
    sortOrder: integer("sort_order").notNull().default(0), // For ordering missions
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at").notNull().defaultNow(),
  },
);

// Season pass mission user table
export const seasonPassMissionUserTable = pchujoyBackendSchema.table(
  "season_pass_mission_user",
  {
    userId: integer("user_id")
      .references(() => userTable.id)
      .notNull(),
    seasonPassId: integer("season_pass_id")
      .references(() => seasonPassTable.id)
      .notNull(),
    seasonPassMissionId: integer("season_pass_mission_id")
      .references(() => seasonPassMissionTable.id)
      .notNull(),
    progressCount: integer("progress_count").notNull().default(1),
    completed: boolean("completed").notNull().default(false),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at").notNull().defaultNow(),
  },
  (table) => [
    primaryKey({ columns: [table.userId, table.seasonPassMissionId] }),
  ],
);
