import { common } from "@mainframe-peru/types";
import {
  boolean,
  jsonb,
  pgSchema,
  serial,
  text,
  timestamp,
} from "drizzle-orm/pg-core";

export const coreBackendSchema = pgSchema("core_backend");

export const genderEnum = coreBackendSchema.enum(
  "gender",
  common.Enums.Gender.options,
);
export const documentTypeEnum = coreBackendSchema.enum(
  "document_type",
  common.Enums.DocumentType.options,
);
export const authenticationTypeEnum = coreBackendSchema.enum(
  "authentication_type",
  common.Enums.AuthenticationType.options,
);
export const influencerStatusEnum = coreBackendSchema.enum(
  "influencer_status",
  common.Enums.InfluencerStatus.options,
);

export const influencerTable = coreBackendSchema.table("influencer", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  status: influencerStatusEnum("status").default("ACTIVE").notNull(),
  logoUrl: text("logo_url").notNull(),
  transientUsers: boolean("transient_users").notNull().default(false),
  domain: text("domain").notNull(),
  attributes: jsonb("attributes").$type<common.AttributeValues>(),
  emailsConfiguration: jsonb().$type<common.EmailsConfiguration>(),
  providersConfiguration: jsonb().$type<common.ProvidersConfiguration>(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const userTable = coreBackendSchema.table("user", {
  id: serial("id").primaryKey(),
  influencerId: text("influencer_id")
    .references(() => influencerTable.id)
    .notNull(),
  authenticationType: authenticationTypeEnum("authentication_type").default(
    "EMAIL",
  ),
  email: text("email").notNull(),
  firstName: text("first_name").notNull(),
  lastName: text("last_name").notNull(),
  alias: text("alias").notNull(),
  hash: text("hash"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
  policies: jsonb("policies").default({}),
  phone: text("phone"),
  gender: genderEnum("gender").default("NO"),
  birthDate: timestamp("birth_date"),
  documentType: documentTypeEnum("document_type"),
  documentValue: text("document_value"),
  companyId: text("company_id"),
  // TODO: move the address info to another table?
  country: text("country"),
  city: text("city"),
  province: text("province"),
  district: text("district"),
  zipCode: text("zip_code"),
  line1: text("line1"),
  line2: text("line2"),
  attributes: jsonb("attributes").$type<common.AttributeValues>(),
  isPartner: boolean("is_partner"),
});
