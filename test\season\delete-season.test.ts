import { pchujoy } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { seasonService } from "../../src/season";
import { TestData, adminAuthToken } from "../common";

describe("delete season tests", () => {
  test("deletes an existing season by id", async () => {
    await seasonService.create(TestData.season);

    const queryParams: pchujoy.season.DeleteSeasonRequest = {
      id: TestData.season.id,
    };

    const response = await request(app)
      .delete("/live/season")
      .query(queryParams)
      .set("Cookie", `session=${await adminAuthToken}`);

    const season = await seasonService.get("id", TestData.season.id);
    expect(response.status).toBe(204);
    expect(season).toBeUndefined;
  });
});
