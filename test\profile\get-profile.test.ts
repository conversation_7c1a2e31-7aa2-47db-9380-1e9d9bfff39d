import request from "supertest";
import { app } from "../../src/api-handler";
import { profileService } from "../../src/profile";
import { TestData, realUserAuthToken } from "../common";
import { sc } from "../../src/services";
import { influencerTable, userTable } from "../../src/core-schema";
import { accessoryService } from "../../src/accessory";

describe("get profile tests", () => {
  test("gets an existing profile by userId", async () => {
    // Create dependencies first
    const db = await sc.getDB();
    await db.insert(influencerTable).values(TestData.influencer);
    await db.insert(userTable).values(TestData.user);

    await Promise.all([
      accessoryService.create(TestData.accessoryGlasses),
      accessoryService.create(TestData.accessoryHat),
      accessoryService.create(TestData.accessoryFace),
      accessoryService.create(TestData.accessoryBackground),
      accessoryService.create(TestData.accessorySkin),
      accessoryService.create(TestData.accessoryHair),
      accessoryService.create(TestData.accessoryTitle),
      accessoryService.create(TestData.accessoryTop),
      accessoryService.create(TestData.accessoryPet),
    ]);

    await profileService.create({
      ...TestData.profile,
      glassesId: TestData.accessoryGlasses.id,
      hatId: TestData.accessoryHat.id,
      faceId: TestData.accessoryFace.id,
      backgroundId: TestData.accessoryBackground.id,
      skinId: TestData.accessorySkin.id,
      hairId: TestData.accessoryHair.id,
      titleId: TestData.accessoryTitle.id,
      topId: TestData.accessoryTop.id,
      petId: TestData.accessoryPet.id,
    });

    const response = await request(app)
      .get("/live/profile")
      .set("Cookie", `session=${await realUserAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      userId: TestData.profile.userId,
      avatarBgColor: expect.any(String),
      petBgColor: expect.any(String),
      avatarLayers: expect.arrayContaining([
        expect.objectContaining({
          id: expect.any(Number),
          name: expect.any(String),
          value: expect.any(String),
          type: expect.any(String),
        }),
      ]),
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    });
  });

  test("returns 200 when profile does not exist", async () => {
    const db = await sc.getDB();
    await db.insert(influencerTable).values(TestData.influencer);
    await db.insert(userTable).values(TestData.user);

    await Promise.all([
      accessoryService.create(TestData.accessoryGlasses),
      accessoryService.create(TestData.accessoryHat),
      accessoryService.create(TestData.accessoryFace),
      accessoryService.create(TestData.accessoryBackground),
      accessoryService.create(TestData.accessorySkin),
      accessoryService.create(TestData.accessoryHair),
      accessoryService.create(TestData.accessoryTitle),
      accessoryService.create(TestData.accessoryTop),
      accessoryService.create(TestData.accessoryPet),
    ]);

    await profileService.create({
      ...TestData.profile,
      glassesId: TestData.accessoryGlasses.id,
      hatId: TestData.accessoryHat.id,
      faceId: TestData.accessoryFace.id,
      backgroundId: TestData.accessoryBackground.id,
      skinId: TestData.accessorySkin.id,
      hairId: TestData.accessoryHair.id,
      titleId: TestData.accessoryTitle.id,
      topId: TestData.accessoryTop.id,
      petId: TestData.accessoryPet.id,
    });

    const response = await request(app)
      .get("/live/profile")
      .set("Cookie", `session=${await realUserAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      userId: TestData.profile.userId,
      avatarBgColor: expect.any(String),
      petBgColor: expect.any(String),
      avatarLayers: expect.arrayContaining([
        expect.objectContaining({
          id: expect.any(Number),
          name: expect.any(String),
          value: expect.any(String),
          type: expect.any(String),
        }),
      ]),
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    });
  });

  test("returns 401 when no authentication token is provided", async () => {
    const queryParams = {
      userId: TestData.profile.userId,
    };

    const response = await request(app).get("/live/profile").query(queryParams);

    expect(response.status).toBe(401);
    expect(response.body.code).toBe("BadAuthorization");
  });
});
