import { AppError } from "@mainframe-peru/common-core";
import { ServiceBase } from "../service-base";
import { SeasonPassEntity, seasonPassRepository } from "./repository";

class SeasonPassService extends ServiceBase<
  typeof seasonPassRepository,
  SeasonPassEntity
> {
  private currentSeasonPass: SeasonPassEntity | undefined = undefined;
  constructor() {
    super(seasonPassRepository);
  }

  async getCurrentSeasonPass(): Promise<SeasonPassEntity> {
    if (
      this.currentSeasonPass &&
      new Date(this.currentSeasonPass.endDate) > new Date() &&
      new Date(this.currentSeasonPass.startDate) < new Date()
    ) {
      return this.currentSeasonPass;
    }
    this.currentSeasonPass = await this.repository.getCurrentSeasonPass();
    if (!this.currentSeasonPass) {
      throw new AppError({
        code: "NoActiveSeasonPass",
        message: "There is no active season pass",
        logLevel: "INFO",
        statusCode: "NOT_FOUND",
      });
    }
    return this.currentSeasonPass;
  }

  clearCache(): void {
    this.currentSeasonPass = undefined;
  }
}

export const seasonPassService = new SeasonPassService();
