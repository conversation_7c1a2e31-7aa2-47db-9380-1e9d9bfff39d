ALTER TYPE "pchujoy"."accessory_type" ADD VALUE 'top';--> statement-breakpoint
ALTER TYPE "pchujoy"."accessory_type" ADD VALUE 'hair';--> statement-breakpoint
ALTER TYPE "pchujoy"."accessory_type" ADD VALUE 'skin';--> statement-breakpoint
ALTER TABLE "pchujoy"."profile" ADD COLUMN "skin_id" integer;--> statement-breakpoint
ALTER TABLE "pchujoy"."profile" ADD COLUMN "hair_id" integer;--> statement-breakpoint
ALTER TABLE "pchujoy"."profile" ADD COLUMN "top_id" integer;--> statement-breakpoint
ALTER TABLE "pchujoy"."profile" ADD COLUMN "pet_bg_color" text;--> statement-breakpoint
ALTER TABLE "pchujoy"."profile" ADD COLUMN "avatar_bg_color" text;--> statement-breakpoint
ALTER TABLE "pchujoy"."profile" ADD CONSTRAINT "profile_skin_id_accessory_id_fk" FOREIGN KEY ("skin_id") REFERENCES "pchujoy"."accessory"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."profile" ADD CONSTRAINT "profile_hair_id_accessory_id_fk" FOREIGN KEY ("hair_id") REFERENCES "pchujoy"."accessory"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."profile" ADD CONSTRAINT "profile_top_id_accessory_id_fk" FOREIGN KEY ("top_id") REFERENCES "pchujoy"."accessory"("id") ON DELETE no action ON UPDATE no action;