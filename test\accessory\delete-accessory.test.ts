import request from "supertest";
import { app } from "../../src/api-handler";
import { AccessoryEntity, accessoryService } from "../../src/accessory";
import { TestData, adminAuthToken, userAuthToken } from "../common";

describe("delete accessory tests", () => {
  let createdAccessory: AccessoryEntity;

  beforeEach(async () => {
    // Create an accessory before each test
    createdAccessory = await accessoryService.create(TestData.accessoryFace);
  });

  test("deletes an existing accessory by id", async () => {
    const queryParams = {
      id: createdAccessory.id,
    };

    const response = await request(app)
      .delete("/live/accessory")
      .query(queryParams)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(204);
    expect(response.body).toEqual({});

    // Verify the accessory was actually deleted from the database
    const deletedAccessory = await accessoryService.get(
      "id",
      createdAccessory.id,
    );
    expect(deletedAccessory).toBeUndefined();
  });

  test("works with user authentication", async () => {
    const queryParams = {
      id: createdAccessory.id,
    };

    const response = await request(app)
      .delete("/live/accessory")
      .query(queryParams)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(204);
    expect(response.body).toEqual({});
  });

  test("returns 400 when id is invalid", async () => {
    const queryParams = {
      id: "invalid", // Invalid accessory ID
    };

    const response = await request(app)
      .delete("/live/accessory")
      .query(queryParams)
      .set("Cookie", `session=${await adminAuthToken}`);
    expect(response.status).toBe(400);
  });

  test("returns 400 when id is missing", async () => {
    const response = await request(app)
      .delete("/live/accessory")
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(400);
  });

  test("returns 401 when no authentication token is provided", async () => {
    const queryParams = {
      id: createdAccessory.id,
    };

    const response = await request(app)
      .delete("/live/accessory")
      .query(queryParams);

    expect(response.status).toBe(401);
  });

  test("handles string id parameter correctly", async () => {
    const queryParams = {
      id: createdAccessory.id.toString(), // String representation of ID
    };

    const response = await request(app)
      .delete("/live/accessory")
      .query(queryParams)
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(response.status).toBe(204);
    expect(response.body).toEqual({});

    // Verify deletion
    const deletedAccessory = await accessoryService.get(
      "id",
      createdAccessory.id,
    );
    expect(deletedAccessory).toBeUndefined();
  });

  test("handles non-existent accessory gracefully", async () => {
    const queryParams = {
      id: 999999, // Non-existent accessory ID
    };

    const response = await request(app)
      .delete("/live/accessory")
      .query(queryParams)
      .set("Cookie", `session=${await adminAuthToken}`);

    // The behavior depends on the service implementation
    // It might return 200 (idempotent) or 404
    expect([200, 404, 500]).toContain(response.status);
  });

  test("can delete multiple accessories", async () => {
    // Create another accessory
    const secondAccessory = await accessoryService.create(
      TestData.accessoryHat,
    );

    // Delete first accessory
    const firstDeleteResponse = await request(app)
      .delete("/live/accessory")
      .query({ id: createdAccessory.id })
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(firstDeleteResponse.status).toBe(204);

    // Delete second accessory
    const secondDeleteResponse = await request(app)
      .delete("/live/accessory")
      .query({ id: secondAccessory.id })
      .set("Cookie", `session=${await adminAuthToken}`);

    expect(secondDeleteResponse.status).toBe(204);

    // Verify both are deleted
    const firstDeleted = await accessoryService.get("id", createdAccessory.id);
    const secondDeleted = await accessoryService.get("id", secondAccessory.id);
    expect(firstDeleted).toBeUndefined();
    expect(secondDeleted).toBeUndefined();
  });
});
