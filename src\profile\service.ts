import { GetProfileResponse } from "@mainframe-peru/types/build/pchujoy/profile";
import { ServiceBase } from "../service-base";
import { ProfileEntity, profileRepository } from "./repository";

class ProfileService extends ServiceBase<
  typeof profileRepository,
  ProfileEntity
> {
  constructor() {
    super(profileRepository);
  }

  formatProfile(
    rawProfile: NonNullable<
      Awaited<ReturnType<typeof profileRepository.getFullProfile>>
    >,
  ) {
    const { profile, ...avatar } = rawProfile;
    const layers: GetProfileResponse["avatarLayers"] = [];

    for (const currentLayer in avatar) {
      const targetLayer = avatar[currentLayer as keyof typeof avatar];
      if (targetLayer) {
        const { id, name, type, value } = targetLayer;
        layers.push({
          id,
          type,
          name,
          value,
        });
      }
    }

    return {
      userId: profile.userId,
      avatarBgColor: profile.avatarBgColor || "",
      petBgColor: profile.petBgColor || "",
      avatarLayers: layers,
      createdAt: profile.createdAt,
      updatedAt: profile.updatedAt,
    };
  }

  async getCurrentProfile(userId: number): Promise<GetProfileResponse> {
    const rawProfile = await this.get("userId", userId);

    if (!rawProfile) {
      await this.create({
        userId,
      });
    }

    const currentProfile = await profileRepository.getFullProfile(userId);
    return this.formatProfile(currentProfile);
  }
}

export const profileService = new ProfileService();
