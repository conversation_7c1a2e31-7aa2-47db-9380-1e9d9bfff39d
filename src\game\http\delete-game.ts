import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { gameService } from "../service";

export const deleteGame: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  null
> = async ({ request, response }) => {
  const { id } = pchujoy.game.DeleteGameRequestSchema.parse(request.query);
  await gameService.delete(id);

  response.body = null;
};
