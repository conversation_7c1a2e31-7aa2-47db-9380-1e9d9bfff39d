import { Middleware } from "@koa/router";
import { seasonPassMissionService } from "../service";
import { pchujoy } from "@mainframe-peru/types";
import { seasonPassService } from "../../season-pass/service";

export const listSeasonPassMissions: Middleware<
  unknown,
  unknown,
  pchujoy.seasonPass.ListSeasonPassMissionsResponse
> = async ({ request, response }) => {
  const body = pchujoy.seasonPass.ListSeasonPassMissionsRequestSchema.parse(
    request.query,
  );

  const currentSeasonPass = await seasonPassService.getCurrentSeasonPass();
  const entities = await seasonPassMissionService.list({
    startAt: body.startAt,
    endAt: body.endAt,
    period: body.period,
    type: body.type,
    isActive: true,
    seasonPassId: currentSeasonPass.id,
  });

  response.set("Cache-Control", "s-maxage=1800");
  response.body = entities.map((e) => ({
    id: e.id,
    name: e.name,
    description: e.description,
    experience: e.experience,
    type: e.type,
    startAt: e.startAt,
    endAt: e.endAt,
    period: e.period,
    action: e.action,
    actionCount: e.actionCount,
    sortOrder: e.sortOrder,
    createdAt: e.createdAt,
    updatedAt: e.updatedAt,
  }));
};
