import { pchujoy } from "@mainframe-peru/types";
import request from "supertest";
import { influencerTable, userTable } from "../../src/core-schema";
import { seasonPassService } from "../../src/season-pass";
import { seasonPassLevelService } from "../../src/season-pass-level";
import { seasonPassMissionService } from "../../src/season-pass-mission";
import { seasonPassMissionUserService } from "../../src/season-pass-mission-user";
import { seasonPassUserProgressService } from "../../src/season-pass-user-progress";
import { sc } from "../../src/services";
import { app } from "../../src/sqs-handler";
import { TestData } from "../common";
import { PublishCommand } from "@aws-sdk/client-sns";

describe("SeasonPassMissionService - evaluateAction", () => {
  test("should add experience directly without checking completed missions", async () => {
    const snsMock = jest.fn();
    sc.sns.send = snsMock;

    const db = await sc.getDB();
    await db.insert(influencerTable).values(TestData.influencer);
    await db.insert(userTable).values(TestData.user);
    await seasonPassService.create(TestData.seasonPass);
    await seasonPassLevelService.create(TestData.seasonPassLevel);
    await seasonPassUserProgressService.create(TestData.seasonPassUserProgress);
    await seasonPassMissionService.create(TestData.seasonPassMission);

    const requestBody: pchujoy.sqs.EvaluateAction = {
      action: TestData.seasonPassMission.action,
      userId: TestData.user.id,
    };

    const response = await request(app)
      .post("/season-pass-mission/evaluate-action")
      .send(requestBody);

    expect(response.status).toEqual(204);

    const userProgress = await seasonPassUserProgressService.get(
      "userId",
      TestData.user.id,
    );
    expect(userProgress?.currentExp).toEqual(
      TestData.seasonPassMission.experience,
    );
    expect(userProgress?.currentLevel).toEqual(TestData.seasonPassLevel.id);

    expect(snsMock).toHaveBeenCalledTimes(1);
    const command = snsMock.mock.lastCall[0] as PublishCommand;
    expect(command.input).toEqual({
      Message: expect.any(String),
      MessageAttributes: {
        service: {
          DataType: "String",
          StringValue: "web-sockets",
        },
      },
      TopicArn: sc.vars.snsTopicArn,
    });
  });

  test("should add experience checking completed missions", async () => {
    const snsMock = jest.fn();
    sc.sns.send = snsMock;

    const db = await sc.getDB();
    await db.insert(influencerTable).values(TestData.influencer);
    await db.insert(userTable).values(TestData.user);
    await seasonPassService.create(TestData.seasonPass);
    await seasonPassLevelService.create(TestData.seasonPassLevel);
    await seasonPassUserProgressService.create(TestData.seasonPassUserProgress);
    const seasonPassMission = await seasonPassMissionService.create({
      ...TestData.seasonPassMission,
      actionCount: 2,
      experience: 23,
    });
    await seasonPassMissionUserService.create({
      seasonPassId: TestData.seasonPass.id,
      seasonPassMissionId: TestData.seasonPassMission.id,
      userId: TestData.user.id,
      progressCount: 1,
      completed: false,
    });

    const requestBody: pchujoy.sqs.EvaluateAction = {
      action: TestData.seasonPassMission.action,
      userId: TestData.user.id,
    };

    const response = await request(app)
      .post("/season-pass-mission/evaluate-action")
      .send(requestBody);

    expect(response.status).toEqual(204);

    const userProgress = await seasonPassUserProgressService.get(
      "userId",
      TestData.user.id,
    );
    expect(userProgress?.currentExp).toEqual(seasonPassMission.experience);
    expect(userProgress?.currentLevel).toEqual(TestData.seasonPassLevel.id);

    const userMission = await seasonPassMissionUserService.get(
      "userId",
      TestData.user.id,
    );
    expect(userMission?.progressCount).toEqual(2);
    expect(userMission?.completed).toEqual(true);

    expect(snsMock).toHaveBeenCalledTimes(1);
  });

  test("should add experience and raise level", async () => {
    const snsMock = jest.fn();
    sc.sns.send = snsMock;

    const db = await sc.getDB();
    await db.insert(influencerTable).values(TestData.influencer);
    await db.insert(userTable).values(TestData.user);
    await seasonPassService.create(TestData.seasonPass);
    await seasonPassLevelService.create(TestData.seasonPassLevel);
    const level = await seasonPassLevelService.create({
      ...TestData.seasonPassLevel,
      id: undefined,
      levelExp: 300,
    });
    await seasonPassLevelService.create({
      ...TestData.seasonPassLevel,
      id: undefined,
      levelExp: 400,
    });
    await seasonPassLevelService.create({
      ...TestData.seasonPassLevel,
      id: undefined,
      levelExp: 500,
    });
    await seasonPassUserProgressService.create(TestData.seasonPassUserProgress);
    const mission = await seasonPassMissionService.create({
      ...TestData.seasonPassMission,
      experience: 400,
    });

    const requestBody: pchujoy.sqs.EvaluateAction = {
      action: TestData.seasonPassMission.action,
      userId: TestData.user.id,
    };

    const response = await request(app)
      .post("/season-pass-mission/evaluate-action")
      .send(requestBody);

    expect(response.status).toEqual(204);

    const userProgress = await seasonPassUserProgressService.get(
      "userId",
      TestData.user.id,
    );
    expect(userProgress?.currentExp).toEqual(mission.experience);
    expect(userProgress?.currentLevel).toEqual(level.id);

    expect(snsMock).toHaveBeenCalledTimes(2);
  });

  test("should not add experience for a repeated mission", async () => {
    const snsMock = jest.fn();
    sc.sns.send = snsMock;

    const db = await sc.getDB();
    await db.insert(influencerTable).values(TestData.influencer);
    await db.insert(userTable).values(TestData.user);
    await seasonPassService.create(TestData.seasonPass);
    await seasonPassLevelService.create(TestData.seasonPassLevel);
    await seasonPassUserProgressService.create(TestData.seasonPassUserProgress);
    await seasonPassMissionService.create(TestData.seasonPassMission);
    await seasonPassMissionUserService.create({
      seasonPassId: TestData.seasonPass.id,
      seasonPassMissionId: TestData.seasonPassMission.id,
      userId: TestData.user.id,
      progressCount: 1,
      completed: true,
    });

    const requestBody: pchujoy.sqs.EvaluateAction = {
      action: TestData.seasonPassMission.action,
      userId: TestData.user.id,
    };

    const response = await request(app)
      .post("/season-pass-mission/evaluate-action")
      .send(requestBody);

    expect(response.status).toEqual(204);

    const userProgress = await seasonPassUserProgressService.get(
      "userId",
      TestData.user.id,
    );
    expect(userProgress?.currentExp).toEqual(
      TestData.seasonPassUserProgress.currentExp,
    );
    expect(userProgress?.currentLevel).toEqual(
      TestData.seasonPassUserProgress.currentLevel,
    );

    const userMission = await seasonPassMissionUserService.get(
      "userId",
      TestData.user.id,
    );
    expect(userMission?.progressCount).toEqual(1);
    expect(userMission?.completed).toEqual(true);

    expect(snsMock).toHaveBeenCalledTimes(0);
  });
});
