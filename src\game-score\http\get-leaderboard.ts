import { Middleware } from "@koa/router";
import { AppError } from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { seasonService } from "../../season";
import { gameScoreService } from "../service";

export const getLeaderboard: Middleware<
  unknown,
  unknown,
  pchujoy.gameScore.GetGameScoreLeaderboardResponse
> = async ({ request, response }) => {
  const body = pchujoy.gameScore.GetGameScoreLeaderboardRequestSchema.parse(
    request.query,
  );
  const season = await seasonService.getCurrentSeason();

  if (!season) {
    throw new AppError({
      code: "NoActiveSeason",
      message: "There is no active season",
      logLevel: "INFO",
      statusCode: "NOT_FOUND",
    });
  }

  const leaderboard = await gameScoreService.getSeasonLeaderboard({
    gameId: body.gameId,
    seasonId: season.id,
  });

  response.set("Cache-Control", "s-maxage=1800");
  response.body = leaderboard;
};
