import { pchujoy } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { gameService } from "../../src/game";
import { TestData } from "../common";
import { seasonService } from "../../src/season";
import { gameSeasonService } from "../../src/game-season";

describe("list game tests", () => {
  beforeEach(async () => {
    for (let i = 0; i < 3; i++) {
      await gameService.create({
        ...TestData.game,
        id: i.toString(),
        status: i === 0 ? "INACTIVE" : "ACTIVE",
      });
    }
  });

  test("list an existing active games", async () => {
    const queryParams: pchujoy.game.ListGamesRequest = {
      status: "ACTIVE",
    };

    const response = await request(app)
      .get("/live/game/list")
      .query(queryParams);

    expect(response.status).toBe(200);
    expect(response.body.length).toEqual(2);
  });

  test("list existing inactive games", async () => {
    const queryParams: pchujoy.game.ListGamesRequest = {
      status: "INACTIVE",
    };

    const response = await request(app)
      .get("/live/game/list")
      .query(queryParams);

    expect(response.status).toBe(200);
    expect(response.body.length).toEqual(1);
    expect(response.body[0]).toEqual({
      createdAt: expect.any(String),
      description: TestData.game.description,
      id: "0",
      imageUrl: TestData.game.imageUrl,
      name: TestData.game.name,
      sourceUrl: TestData.game.sourceUrl,
      status: "INACTIVE",
      updatedAt: expect.any(String),
    });
  });

  test("list games by seasonId", async () => {
    await seasonService.create(TestData.season);
    for (let i = 0; i < 3; i++) {
      await gameSeasonService.create({
        gameId: i.toString(),
        seasonId: TestData.season.id,
      });
    }
    const queryParams: pchujoy.game.ListGamesRequest = {
      seasonId: TestData.season.id,
    };

    const response = await request(app)
      .get("/live/game/list")
      .query(queryParams);

    expect(response.status).toBe(200);
    expect(response.body.length).toEqual(3);
  });
});
