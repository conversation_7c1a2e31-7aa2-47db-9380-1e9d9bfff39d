import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { gameSeasonService } from "../service";

export const deleteGameSeason: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  null
> = async ({ request, response }) => {
  const { gameId, seasonId } =
    pchujoy.gameSeason.DeleteGameSeasonRequestSchema.parse(request.query);
  await gameSeasonService.deleteBySeasonAndGame(seasonId, gameId);

  response.body = null;
};
