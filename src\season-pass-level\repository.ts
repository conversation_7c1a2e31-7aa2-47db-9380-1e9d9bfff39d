import { asc, eq, InferSelectModel } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { seasonPassLevelTable } from "../schema";
import { sc } from "../services";

export type SeasonPassLevelEntity = InferSelectModel<
  typeof seasonPassLevelTable
>;

class SeasonPassLevelRepository extends RepositoryBase<
  typeof seasonPassLevelTable,
  number
> {
  constructor() {
    super(seasonPassLevelTable);
  }

  async getBySeasonPassId(
    seasonPassId: number,
  ): Promise<SeasonPassLevelEntity[]> {
    const db = await sc.getDB();
    const result = await db
      .select()
      .from(this.table)
      .where(eq(this.table.seasonPassId, seasonPassId))
      .orderBy(asc(this.table.levelIndex));
    return result;
  }

  async getFirstLevel(seasonPassId: number): Promise<SeasonPassLevelEntity> {
    const db = await sc.getDB();
    const result = await db
      .select()
      .from(this.table)
      .where(eq(this.table.seasonPassId, seasonPassId))
      .orderBy(asc(this.table.levelExp));
    return result[0];
  }

  async getByLevelIndex(seasonPassId: number): Promise<SeasonPassLevelEntity> {
    const db = await sc.getDB();
    const result = await db
      .select()
      .from(this.table)
      .where(eq(this.table.seasonPassId, seasonPassId))
      .orderBy(asc(this.table.levelExp));
    return result[0];
  }
}

export const seasonPassLevelRepository = new SeasonPassLevelRepository();
