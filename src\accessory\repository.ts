import { InferSelectModel } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { accessoryTable } from "../schema";

export type AccessoryEntity = InferSelectModel<typeof accessoryTable>;

class AccessoryRepository extends RepositoryBase<
  typeof accessoryTable,
  number
> {
  constructor() {
    super(accessoryTable);
  }
}

export const accessoryRepository = new AccessoryRepository();
