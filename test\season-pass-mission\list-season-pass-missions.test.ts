import supertest from "supertest";
import { app } from "../../src/api-handler";
import { influencerTable, userTable } from "../../src/core-schema";
import { seasonPassService } from "../../src/season-pass";
import { seasonPassMissionService } from "../../src/season-pass-mission";
import { sc } from "../../src/services";
import { TestData } from "../common";

const request = supertest;

describe("GET /live/season-pass-mission", () => {
  it("should return empty array when no missions exist", async () => {
    await seasonPassService.create(TestData.seasonPass);
    const response = await request(app)
      .get("/live/season-pass-mission")
      .expect(200);

    expect(response.body).toEqual([]);
  });

  describe("with test data", () => {
    beforeEach(async () => {
      const db = await sc.getDB();
      await db.insert(influencerTable).values(TestData.influencer);
      await db.insert(userTable).values(TestData.user);
      await seasonPassService.create(TestData.seasonPass);
      await seasonPassMissionService.create(TestData.seasonPassMission);
    });

    it("should return missions and filter by seasonPassId", async () => {
      const response = await request(app)
        .get("/live/season-pass-mission")
        .expect(200);

      expect(response.body).toHaveLength(1);
      expect(response.body[0]).toMatchObject({
        id: TestData.seasonPassMission.id,
        name: TestData.seasonPassMission.name,
        description: TestData.seasonPassMission.description,
        experience: TestData.seasonPassMission.experience,
        type: TestData.seasonPassMission.type,
        period: TestData.seasonPassMission.period,
        action: TestData.seasonPassMission.action,
        actionCount: TestData.seasonPassMission.actionCount,
        sortOrder: TestData.seasonPassMission.sortOrder,
      });
    });

    it("should filter by period", async () => {
      const response = await request(app)
        .get("/live/season-pass-mission")
        .query({ period: "daily" })
        .expect(200);

      expect(response.body).toHaveLength(1);
      expect(response.body[0].period).toBe("daily");
    });

    it("should filter by type", async () => {
      const response = await request(app)
        .get("/live/season-pass-mission")
        .query({ type: "regular" })
        .expect(200);

      expect(response.body).toHaveLength(1);
      expect(response.body[0].type).toBe("regular");
    });
  });

  describe("validation", () => {
    it("should return 400 for invalid period", async () => {
      await request(app)
        .get("/live/season-pass-mission")
        .query({ period: "invalid" })
        .expect(400);
    });

    it("should return 400 for invalid type", async () => {
      await request(app)
        .get("/live/season-pass-mission")
        .query({ type: "invalid" })
        .expect(400);
    });

    it("should return 400 for invalid date format", async () => {
      await request(app)
        .get("/live/season-pass-mission")
        .query({ startAt: "invalid-date" })
        .expect(400);
    });
  });
});
