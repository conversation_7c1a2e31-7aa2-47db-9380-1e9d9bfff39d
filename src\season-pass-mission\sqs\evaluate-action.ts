import { Middleware } from "@koa/router";
import { pchujoy } from "@mainframe-peru/types";
import { seasonPassMissionService } from "../service";

export const evaluateAction: Middleware<unknown, unknown, null> = async (
  ctx,
) => {
  const message = pchujoy.sqs.EvaluateActionSchema.parse(ctx.request.body);
  ctx.response.body = null;

  await seasonPassMissionService.evaluateAction({
    action: message.action,
    userId: message.userId,
  });
};
