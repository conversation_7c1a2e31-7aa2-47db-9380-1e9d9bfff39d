import { Middleware } from "@koa/router";
import {
  AppError,
  Auth,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { accessoryService } from "../service";

export const updateAccessory: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  pchujoy.accessory.UpdateAccessoryResponse
> = async ({ request, response }) => {
  const { id, ...update } =
    pchujoy.accessory.UpdateAccessoryRequestSchema.parse(request.body);

  const accessory = await accessoryService.get("id", id);
  if (!accessory) {
    throw new AppError({
      code: "AccessoryNotFound",
      message: "No se encontró el accesorio solicitado",
      statusCode: "NOT_FOUND",
    });
  }

  const updatedAccessory = await accessoryService.update(id, update);

  response.body =
    pchujoy.accessory.UpdateAccessoryResponseSchema.parse(updatedAccessory);
};
