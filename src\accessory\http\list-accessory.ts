import { Middleware } from "@koa/router";
import { pchujoy } from "@mainframe-peru/types";
import { accessoryService } from "../service";

export const listAccessory: Middleware<
  unknown,
  unknown,
  pchujoy.accessory.ListAccessoryResponse
> = async ({ request, response }) => {
  const body = pchujoy.accessory.ListAccessoryRequestSchema.parse(
    request.query,
  );

  const filter = body.type ? { type: body.type } : undefined;
  const page =
    body.limit || body.offset
      ? {
          limit: body.limit,
          offset: body.offset,
        }
      : undefined;

  const entities = await accessoryService.list(filter, page);

  response.set("Cache-Control", "s-maxage=1800");
  response.body = entities.map((e) => ({
    id: e.id,
    name: e.name,
    type: e.type,
    value: e.value,
    createdAt: e.createdAt,
    updatedAt: e.updatedAt,
  }));
};
