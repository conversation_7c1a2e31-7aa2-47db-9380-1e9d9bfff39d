import request from "supertest";
import { app } from "../../src/api-handler";
import { accessoryService } from "../../src/accessory";
import { TestData, userAuthToken } from "../common";

describe("get accessory tests", () => {
  test("gets an existing accessory by id", async () => {
    // Create an accessory first
    const createdAccessory = await accessoryService.create(
      TestData.accessoryFace,
    );

    const queryParams = {
      id: createdAccessory.id,
    };

    const response = await request(app)
      .get("/live/accessory")
      .query(queryParams)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      id: createdAccessory.id,
      name: TestData.accessoryFace.name,
      type: TestData.accessoryFace.type,
      value: TestData.accessoryFace.value,
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    });
  });

  test("returns 404 when accessory does not exist", async () => {
    const queryParams = {
      id: 999999, // Non-existent accessory ID
    };

    const response = await request(app)
      .get("/live/accessory")
      .query(queryParams)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      code: "AccessoryNotFound",
      message: "No se encontró el accesorio solicitado",
    });
  });

  test("returns 400 when id is invalid", async () => {
    const queryParams = {
      id: "invalid", // Invalid accessory ID
    };

    const response = await request(app)
      .get("/live/accessory")
      .query(queryParams)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(400);
  });

  test("returns 400 when id is missing", async () => {
    const response = await request(app)
      .get("/live/accessory")
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(400);
  });

  test("sets cache control header", async () => {
    const createdAccessory = await accessoryService.create(
      TestData.accessoryFace,
    );

    const queryParams = {
      id: createdAccessory.id,
    };

    const response = await request(app)
      .get("/live/accessory")
      .query(queryParams)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.headers["cache-control"]).toBe("s-maxage=1800");
  });

  test("handles string id parameter correctly", async () => {
    const createdAccessory = await accessoryService.create(
      TestData.accessoryFace,
    );

    const queryParams = {
      id: createdAccessory.id.toString(), // String representation of ID
    };

    const response = await request(app)
      .get("/live/accessory")
      .query(queryParams)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body.id).toBe(createdAccessory.id);
  });
});
