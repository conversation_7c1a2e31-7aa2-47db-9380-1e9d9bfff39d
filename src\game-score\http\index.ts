import Router from "@koa/router";
import { getClientAuthMiddleware } from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { createGameScore } from "./create";
import { getLeaderboard } from "./get-leaderboard";
import { getUserScores } from "./get-user-game-scores";
import { getUserTopScores } from "./get-user-top-scores";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

// Game season
router.post("/", authMiddleware, createGameScore);
router.get("/user/game", authMiddleware, getUserScores);
router.get("/user", authMiddleware, getUserTopScores);
router.get("/leaderboard", getLeaderboard);

export const gameScoreRouter = router;
