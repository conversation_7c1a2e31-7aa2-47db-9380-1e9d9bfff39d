import { Policies, createJWT } from "@mainframe-peru/common-core";
import { userPoliciesConstant } from "@mainframe-peru/common-core/build/policies/lib/modules/user";
import { sc } from "../src/services";
import { GameEntity } from "../src/game/repository";
import { SeasonEntity } from "../src/season/repository";
import { SeasonPassEntity } from "../src/season-pass/repository";
import { InferSelectModel } from "drizzle-orm";
import { common } from "@mainframe-peru/types";
import { influencerTable, userTable } from "../src/core-schema";
import { SeasonPassMissionEntity } from "../src/season-pass-mission";
import { SeasonPassLevelEntity } from "../src/season-pass-level";
import { SeasonPassUserProgressEntity } from "../src/season-pass-user-progress";
import { ProfileEntity } from "../src/profile";
import { AccessoryEntity } from "../src/accessory";
import { Reward } from "@mainframe-peru/types/build/pchujoy/season-pass";

const game: Readonly<GameEntity> = {
  id: "g1",
  status: "ACTIVE",
  name: "game",
  description: "lorem ipsum",
  imageUrl: "/test/",
  sourceUrl: "/source/",
  createdAt: new Date(),
  updatedAt: new Date(),
};

const season: Readonly<SeasonEntity> = {
  id: 1234,
  name: "initial season",
  startDate: "2020-01-01",
  endDate: "2030-01-01",
  createdAt: new Date(),
  updatedAt: new Date(),
};

const influencer: Readonly<InferSelectModel<typeof influencerTable>> = {
  id: "PchujoyTest",
  name: "Bizz name",
  status: common.Enums.InfluencerStatus.Enum.ACTIVE,
  logoUrl: "www",
  transientUsers: false,
  domain: "fcc.pchujoy.app",
  createdAt: new Date(),
  updatedAt: new Date(),
  attributes: null,
  emailsConfiguration: {
    templates: {},
  },
  providersConfiguration: {},
};

const user: Readonly<InferSelectModel<typeof userTable>> = {
  id: *********,
  influencerId: influencer.id,
  authenticationType: "EMAIL",
  email: "<EMAIL>",
  firstName: "Sebastián",
  lastName: "Muñoz",
  alias: "OptimistaLima7",
  phone: "*********",
  gender: common.Enums.Gender.Enum.F,
  createdAt: new Date(),
  updatedAt: new Date(),
  policies: {},
  documentType: common.Enums.DocumentType.Enum.DNI,
  documentValue: "12345678",
  city: "Lima",
  country: common.Enums.Country.Enum.PE,
  line1: "test address 1",
  province: "Lima",
  birthDate: new Date(),
  companyId: "*********",
  district: "",
  hash: "",
  line2: "",
  zipCode: "123123",
  attributes: null,
  isPartner: false,
};

const accessoryGlasses: Readonly<AccessoryEntity> = {
  id: 923423,
  name: "Cool Sunglasses",
  type: "glasses" as const,
  value: "https://example.com/sunglasses.png",
  createdAt: new Date(),
  updatedAt: new Date(),
};

const accessoryHat: Readonly<AccessoryEntity> = {
  id: 43593,
  name: "Baseball Cap",
  type: "hat" as const,
  value: "https://example.com/cap.png",
  createdAt: new Date(),
  updatedAt: new Date(),
};

const accessoryFace: Readonly<AccessoryEntity> = {
  id: 93452,
  name: "Smile",
  type: "face" as const,
  value: "https://example.com/smile.png",
  createdAt: new Date(),
  updatedAt: new Date(),
};

const accessoryBackground: Readonly<AccessoryEntity> = {
  id: 112233,
  name: "Beach Background",
  type: "background" as const,
  value: "https://example.com/beach.png",
  createdAt: new Date(),
  updatedAt: new Date(),
};

const accessorySkin: Readonly<AccessoryEntity> = {
  id: 223344,
  name: "Light Skin",
  type: "skin" as const,
  value: "https://example.com/skin.png",
  createdAt: new Date(),
  updatedAt: new Date(),
};

const accessoryHair: Readonly<AccessoryEntity> = {
  id: 334455,
  name: "Short Hair",
  type: "hair" as const,
  value: "https://example.com/hair.png",
  createdAt: new Date(),
  updatedAt: new Date(),
};

const accessoryTitle: Readonly<AccessoryEntity> = {
  id: 445566,
  name: "Champion",
  type: "title" as const,
  value: "Michiiiiiii 💀",
  createdAt: new Date(),
  updatedAt: new Date(),
};

const accessoryTop: Readonly<AccessoryEntity> = {
  id: 556677,
  name: "T-Shirt",
  type: "top" as const,
  value: "https://example.com/tshirt.png",
  createdAt: new Date(),
  updatedAt: new Date(),
};

const accessoryPet: Readonly<AccessoryEntity> = {
  id: 667788,
  name: "Cute Cat",
  type: "pet" as const,
  value: "https://example.com/cat.png",
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const rewards: Reward[] = [
  {
    id: accessoryBackground.id,
    type: "ACCESSORY",
    content: {
      id: accessoryBackground.id,
      name: accessoryBackground.name,
      type: accessoryBackground.type,
      value: accessoryBackground.value,
    },
  },
  {
    id: 123123,
    type: "EVENT",
    content: {
      id: 123123,
      description: "Some test special event",
      imageUrl: "https://www.example.com",
      name: "Test Special Event",
      type: "PRIZE",
    },
  },
];

const profile: Readonly<ProfileEntity> = {
  userId: user.id,
  // avatar accessories (solo IDs)
  backgroundId: null,
  skinId: null,
  faceId: null,
  hairId: accessoryHair.id,
  hatId: accessoryHat.id,
  glassesId: null,
  titleId: null,
  topId: null,
  petId: null,

  // colors
  avatarBgColor: "#F2AE27",
  petBgColor: "#276BF2",

  // timestamps
  createdAt: new Date(),
  updatedAt: new Date(),
};

const seasonPass: Readonly<SeasonPassEntity> = {
  id: 126569,
  name: "Test Season Pass",
  bannerUrl: "https://example.com/banner.png",
  startDate: "2020-01-01",
  endDate: "2030-01-01",
  createdAt: new Date(),
  updatedAt: new Date(),
};

const seasonPassLevel: Readonly<SeasonPassLevelEntity> = {
  id: 452357,
  seasonPassId: seasonPass.id,
  levelIndex: 0,
  name: "Test season pass level",
  description: "description",
  levelExp: 0,
  rewards,
  createdAt: new Date(),
  updatedAt: new Date(),
};

const seasonPassUserProgress: Readonly<SeasonPassUserProgressEntity> = {
  userId: user.id,
  seasonPassId: seasonPass.id,
  currentExp: 0,
  currentLevel: seasonPassLevel.id,
  levelsClaimed: [],
  createdAt: new Date(),
  updatedAt: new Date(),
};

const seasonPassMission: Readonly<SeasonPassMissionEntity> = {
  id: 9876576,
  seasonPassId: seasonPass.id,
  name: "Play Game Mission",
  description: "Play any game",
  experience: 100,
  isActive: true,
  type: "regular" as const,
  startAt: new Date("2020-01-01"),
  endAt: new Date("2030-01-01"),
  action: "LOGIN",
  actionCount: 1,
  period: "daily",
  sortOrder: 0,
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const TestData = {
  officerId: 1234567,
  adminId: 1234567,
  game,
  season,
  seasonPass,
  seasonPassMission,
  seasonPassLevel,
  seasonPassUserProgress,
  user,
  influencer,
  profile,
  rewards,
  accessoryGlasses,
  accessoryHat,
  accessoryFace,
  accessoryBackground,
  accessorySkin,
  accessoryHair,
  accessoryTitle,
  accessoryTop,
  accessoryPet,
  avatarBgColor: "#F2AE27",
  petBgColor: "#276BF2",
} as const;

export const officerAuthToken = createJWT(
  "officer",
  {
    id: TestData.officerId,
    email: "<EMAIL>",
    firstName: "John",
    lastName: "Doe",
    policies: {
      plan: 15,
      card: 15,
      officer: 15,
      user: 15,
      admin: 15,
      influencer: 15,
      transaction: 15,
      payment: 15,
      recurrence: 15,
      complaint: 15,
      event: 31,
    },
  },
  sc.vars.keys.officer.private,
  Math.floor(Date.now() / 1000) + 60, // expires in 60 seconds
);

export const adminAuthToken = createJWT(
  "admin",
  {
    id: TestData.adminId,
    influencerId: TestData.influencer.id,
    email: "<EMAIL>",
    firstName: "John",
    lastName: "Doe",
    policies: {
      plan: 15,
      card: 15,
      user: 15,
      admin: 15,
      transaction: 15,
      influencer: 15,
      payment: 15,
      recurrence: 15,
      complaint: 15,
      invoice: 3,
      paymentProviderEvent: 15,
      event: 31,
      business: 15,
      businessPromotion: 15,
      businessPromotionCode: 15,
      attributeValue: 15,
    },
  },
  sc.vars.keys.admin.private,
  Math.floor(Date.now() / 1000) + 60, // expires in 60 seconds
);

export const realUserAuthToken = createJWT(
  "user",
  {
    id: TestData.user.id,
    influencerId: TestData.influencer.id,
    email: "<EMAIL>",
    firstName: "John",
    lastName: "Doe",
    policies: Policies.mask(
      {
        general: {
          REGULAR: true,
          TRANSIENT: true,
        },
      },
      userPoliciesConstant,
    ),
  },
  sc.vars.keys.user.private,
  Math.floor(Date.now() / 1000) + 60, // expires in 60 seconds
);

export const userAuthToken = createJWT(
  "admin",
  {
    id: TestData.user.id,
    influencerId: TestData.influencer.id,
    email: "<EMAIL>",
    firstName: "John",
    lastName: "Doe",
    policies: {
      plan: 15,
      card: 15,
      user: 15,
      admin: 15,
      transaction: 15,
      influencer: 15,
      payment: 15,
      recurrence: 15,
      complaint: 15,
      invoice: 3,
      paymentProviderEvent: 15,
      event: 31,
      business: 15,
      businessPromotion: 15,
      businessPromotionCode: 15,
      attributeValue: 15,
    },
  },
  sc.vars.keys.admin.private,
  Math.floor(Date.now() / 1000) + 60, // expires in 60 seconds
);
