import request from "supertest";
import { app } from "../../src/api-handler";
import { gameService } from "../../src/game";
import { GameEntity } from "../../src/game/repository";
import { gameScoreService } from "../../src/game-score";
import { gameSeasonService } from "../../src/game-season";
import { seasonService } from "../../src/season";
import { TestData, userAuthToken } from "../common";
import { sc } from "../../src/services";
import { influencerTable, userTable } from "../../src/core-schema";

describe("get games with user scores tests", () => {
  test("get games with user scores", async () => {
    const db = await sc.getDB();
    await db.insert(influencerTable).values(TestData.influencer);
    await db.insert(userTable).values(TestData.user);

    const games: GameEntity[] = [];
    for (let i = 0; i < 3; i++) {
      const game = await gameService.create({
        ...TestData.game,
        id: i.toString(),
      });
      games.push(game);
    }

    await seasonService.create(TestData.season);

    // Add games to season
    for (const game of games) {
      await gameSeasonService.create({
        gameId: game.id,
        seasonId: TestData.season.id,
      });
    }

    await gameScoreService.create({
      gameId: games[0].id,
      seasonId: TestData.season.id,
      userId: TestData.user.id,
      score: 100,
    });

    await gameScoreService.create({
      gameId: games[1].id,
      seasonId: TestData.season.id,
      userId: TestData.user.id,
      score: 200,
    });

    const response = await request(app)
      .get("/live/game-score/user")
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body.length).toEqual(2);

    expect(response.body[0].id).toBe(games[1].id);
    expect(response.body[0].topScore).toBe(200);

    expect(response.body[1].id).toBe(games[0].id);
    expect(response.body[1].topScore).toBe(100);
  });
});
