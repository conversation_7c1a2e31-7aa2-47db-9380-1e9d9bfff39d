import { pchujoy } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { seasonService } from "../../src/season";
import { TestData, adminAuthToken } from "../common";

describe("create season tests", () => {
  test("create an existing season by id", async () => {
    const requestBody: pchujoy.season.CreateSeasonRequest = TestData.season;

    const response = await request(app)
      .post("/live/season")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    const responseBody: pchujoy.season.CreateSeasonResponse = response.body;
    const season = await seasonService.get("id", responseBody.id);
    expect(response.status).toBe(200);
    expect(season).toEqual({
      ...TestData.season,
      id: responseBody.id,
      createdAt: expect.any(Date),
      updatedAt: expect.any(Date),
    });
  });
});
