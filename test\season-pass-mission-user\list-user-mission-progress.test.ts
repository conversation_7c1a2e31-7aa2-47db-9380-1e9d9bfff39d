import supertest from "supertest";
import { app } from "../../src/api-handler";
import { influencerTable, userTable } from "../../src/core-schema";
import { seasonPassService } from "../../src/season-pass";
import { seasonPassMissionService } from "../../src/season-pass-mission";
import { seasonPassMissionUserService } from "../../src/season-pass-mission-user";
import { sc } from "../../src/services";
import { TestData, userAuthToken } from "../common";

const request = supertest;

describe("GET /live/season-pass-mission-user/progress", () => {
  it("should return user mission progress", async () => {
    const db = await sc.getDB();
    await db.insert(influencerTable).values(TestData.influencer);
    await db.insert(userTable).values(TestData.user);
    await seasonPassService.create(TestData.seasonPass);

    for (let i = 0; i < 10; i++) {
      const m = await seasonPassMissionService.create({
        seasonPassId: TestData.seasonPass.id,
        name: "Play Game Mission",
        description: "Play any game",
        experience: 100,
        isActive: true,
        type: "regular" as const,
        startAt: new Date("2020-01-01"),
        endAt: new Date("2030-01-01"),
        action: "LOGIN",
        period: "daily",
      });
      await seasonPassMissionUserService.create({
        seasonPassId: TestData.seasonPass.id,
        seasonPassMissionId: m.id,
        userId: TestData.user.id,
        completed: false,
        progressCount: 1,
      });
    }

    const response = await request(app)
      .get("/live/season-pass-mission-user/progress")
      .set("Cookie", `session=${await userAuthToken}`)
      .expect(200);

    expect(response.body).toHaveProperty("missions");
    expect(Array.isArray(response.body.missions)).toBe(true);
    expect(response.body.missions.length).toEqual(10);

    // If there are missions, check the structure
    if (response.body.missions.length > 0) {
      const mission = response.body.missions[0];
      expect(mission).toHaveProperty("missionId");
      expect(mission).toHaveProperty("progressCount");
      expect(mission).toHaveProperty("completed");
      expect(mission).toHaveProperty("createdAt");
      expect(mission).toHaveProperty("updatedAt");

      expect(typeof mission.missionId).toBe("number");
      expect(typeof mission.progressCount).toBe("number");
      expect(typeof mission.completed).toBe("boolean");

      // Should not include userId or seasonPassId
      expect(mission).not.toHaveProperty("userId");
      expect(mission).not.toHaveProperty("seasonPassId");
    }
  });

  it("should require authentication", async () => {
    await request(app)
      .get("/live/season-pass-mission-user/progress")
      .expect(401);
  });
});
