import serverlessExpress from "@codegenie/serverless-express";
import { bodyParser } from "@koa/bodyparser";
import Router from "@koa/router";
import {
  errorMiddleware,
  logger,
  loggerMiddleware,
} from "@mainframe-peru/common-core";
import { sql } from "drizzle-orm";
import <PERSON><PERSON> from "koa";
import { accessoryRouter } from "./accessory";
import { gameRouter } from "./game";
import { gameScoreRouter } from "./game-score";
import { gameSeasonRouter } from "./game-season";
import { inventoryRouter } from "./inventory";
import { profileRouter } from "./profile";
import { seasonRouter } from "./season";
import { seasonPassRouter } from "./season-pass";
import { seasonPassLevelRouter } from "./season-pass-level";
import { seasonPassMissionRouter } from "./season-pass-mission";
import { seasonPassMissionUserRouter } from "./season-pass-mission-user";
import { seasonPassUserProgressRouter } from "./season-pass-user-progress";
import { sc } from "./services";

const koa = new Koa();
const router = new Router({
  prefix: "/live",
});

/**
 * Middlewares
 */
koa.use(errorMiddleware);
koa.use(bodyParser());
koa.use(loggerMiddleware);
koa.use(async (_, next) => {
  try {
    await next();
  } catch (e) {
    if (
      e instanceof Error &&
      e.message.includes("password authentication failed")
    ) {
      await sc.refreshDb();
      await next();
    } else {
      throw e;
    }
  }
});

/**
 * Setup routes
 */
router.use("/accessory", accessoryRouter.routes());
router.use("/game", gameRouter.routes());
router.use("/inventory", inventoryRouter.routes());
router.use("/profile", profileRouter.routes());
router.use("/season", seasonRouter.routes());
router.use("/season-pass", seasonPassRouter.routes());
router.use("/season-pass-level", seasonPassLevelRouter.routes());
router.use("/season-pass-mission", seasonPassMissionRouter.routes());
router.use("/season-pass-mission-user", seasonPassMissionUserRouter.routes());
router.use("/season-pass-user-progress", seasonPassUserProgressRouter.routes());
router.use("/game-season", gameSeasonRouter.routes());
router.use("/game-score", gameScoreRouter.routes());

/**
 * Health route
 */
router.get("/health", async ({ response }) => {
  try {
    const db = await sc.getDB();
    const statement = sql`SELECT version()`;
    const res = await db.execute(statement);
    logger.info("Got postgres version");
    logger.info(res.rows[0].version as string);
    response.body = {
      ok: true,
    };
  } catch (e) {
    logger.error(e instanceof Error ? e : "");
    throw e;
  }
});

/**
 * Finish setup
 */
koa.use(router.routes());

export const app = koa.callback();
export const handler = serverlessExpress({ app });
