import { pchujoy } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { gameService } from "../../src/game";
import { TestData, adminAuthToken } from "../common";

describe("update game tests", () => {
  test("updates an existing game by id", async () => {
    await gameService.create(TestData.game);

    const requestBody: pchujoy.game.UpdateGameRequest = {
      id: TestData.game.id,
      name: crypto.randomUUID(),
    };

    const response = await request(app)
      .put("/live/game")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    const game = await gameService.get("id", TestData.game.id);
    expect(response.status).toBe(200);
    expect(game?.name).toEqual(requestBody.name);
  });
});
