import { pchujoy } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { gameService } from "../../src/game";
import { gameScoreService } from "../../src/game-score";
import { gameSeasonService } from "../../src/game-season";
import { seasonService } from "../../src/season";
import { TestData } from "../common";
import { sc } from "../../src/services";
import { influencerTable, userTable } from "../../src/core-schema";

describe("get game score leader board tests", () => {
  test("get game score leader board", async () => {
    const db = await sc.getDB();
    await db.insert(influencerTable).values(TestData.influencer);
    await gameService.create(TestData.game);
    await seasonService.create(TestData.season);
    await gameSeasonService.create({
      gameId: TestData.game.id,
      seasonId: TestData.season.id,
    });
    for (let i = 0; i < 1000; i++) {
      const userId = TestData.user.id + i;
      await db.insert(userTable).values({
        ...TestData.user,
        id: userId,
      });
      await gameScoreService.create({
        gameId: TestData.game.id,
        seasonId: TestData.season.id,
        userId,
        score: i,
      });
    }

    const requestBody: pchujoy.gameScore.GetGameScoreLeaderboardRequest = {
      gameId: TestData.game.id,
    };

    const response = await request(app)
      .get("/live/game-score/leaderboard")
      .query(requestBody);

    const responseBody: pchujoy.gameScore.GetGameScoreLeaderboardResponse =
      response.body;

    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(10);
  });
});
