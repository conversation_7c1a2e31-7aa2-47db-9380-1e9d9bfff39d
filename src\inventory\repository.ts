import { and, eq, InferSelectModel } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { accessoryTable, inventoryTable } from "../schema";
import { sc } from "../services";

export type InventoryEntity = InferSelectModel<typeof inventoryTable>;

class InventoryRepository extends RepositoryBase<
  typeof inventoryTable,
  { accessoryId: number; userId: number }
> {
  constructor() {
    super(inventoryTable);
  }

  async getInventory(userId: number) {
    const db = await sc.getDB();
    return await db
      .select({
        id: accessoryTable.id,
        name: accessoryTable.name,
        type: accessoryTable.type,
        value: accessoryTable.value,
      })
      .from(this.table)
      .innerJoin(accessoryTable, eq(this.table.accessoryId, accessoryTable.id))
      .where(eq(this.table.userId, userId));
  }

  async findAccessoryInInventory(userId: number, accessoryId: number): Promise<InventoryEntity> {
    const db = await sc.getDB();
    const result = await db
      .select()
      .from(this.table)
      .where(and(eq(this.table.userId, userId), eq(this.table.accessoryId, accessoryId)))
    ;
    return result[0];
  }
}

export const inventoryRepository = new InventoryRepository();
