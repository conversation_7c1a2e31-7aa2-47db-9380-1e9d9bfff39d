import { Middleware } from "@koa/router";
import { AppError } from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { seasonService } from "../service";

export const getSeason: Middleware<
  unknown,
  unknown,
  pchujoy.season.GetSeasonResponse
> = async ({ request, response }) => {
  const params = pchujoy.season.GetSeasonRequestSchema.parse(request.query);
  const entity = await seasonService.get("id", params.id);

  if (!entity) {
    throw new AppError({
      code: "InfluencerNotFound",
      message: "No se encontró al influencer solicitado",
      statusCode: "NOT_FOUND",
    });
  }

  response.set("Cache-Control", "s-maxage=1800");
  response.body = pchujoy.season.GetSeasonResponseSchema.parse(entity);
};
