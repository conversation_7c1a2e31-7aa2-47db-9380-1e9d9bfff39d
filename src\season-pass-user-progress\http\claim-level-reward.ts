import { Middleware } from "@koa/router";
import {
  AppError,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { seasonPassLevelService } from "../../season-pass-level";

export const claimLevelReward: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  null
> = async ({ response, request, state }) => {
  const body = pchujoy.seasonPass.ClaimLevelRewardRequestSchema.parse(
    request.body,
  );

  await seasonPassLevelService.claimLevelReward(state.auth.id, body.levelId);

  response.body = null;
};
 