import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { seasonService } from "../service";

export const createSeason: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  pchujoy.season.CreateSeasonResponse
> = async ({ request, response }) => {
  const body = pchujoy.season.CreateSeasonRequestSchema.parse(request.body);
  const season = await seasonService.create(body);

  response.body = {
    id: season.id,
    name: season.name,
    startDate: season.startDate,
    endDate: season.endDate,
    createdAt: season.createdAt,
    updatedAt: season.updatedAt,
  };
};
