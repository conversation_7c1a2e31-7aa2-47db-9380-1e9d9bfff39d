import { and, gte, InferSelectModel, lte, sql } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { seasonPassTable } from "../schema";
import { sc } from "../services";

export type SeasonPassEntity = InferSelectModel<typeof seasonPassTable>;

class SeasonPassRepository extends RepositoryBase<
  typeof seasonPassTable,
  number
> {
  constructor() {
    super(seasonPassTable);
  }

  async getCurrentSeasonPass(): Promise<SeasonPassEntity | undefined> {
    const db = await sc.getDB();
    const result = await db
      .select()
      .from(this.table)
      .where(
        and(
          gte(this.table.endDate, sql`now()::date`),
          lte(this.table.startDate, sql`now()::date`),
        ),
      )
      .limit(1);
    return result[0];
  }
}

export const seasonPassRepository = new SeasonPassRepository();
