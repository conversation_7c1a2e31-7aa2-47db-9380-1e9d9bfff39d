import { AppError } from "@mainframe-peru/common-core";
import {
  and,
  eq,
  InferInsertModel,
  InferSelectModel,
  isNull,
  SQL,
} from "drizzle-orm";
import { PgTableWithColumns } from "drizzle-orm/pg-core";
import { addLimitAndOffsetToQuery, transformNullToUndefined } from "./common";
import { sc } from "./services";
import { gameTable } from "./schema";

export abstract class RepositoryBase<
  T extends PgTableWithColumns<any>, // eslint-disable-line @typescript-eslint/no-explicit-any
  K = number,
  Entity = InferSelectModel<T>,
> {
  protected table: T;

  constructor(table: T) {
    this.table = table;
  }

  // Find by any column
  async find(
    column: keyof Entity,
    value: string | number,
  ): Promise<Entity | undefined> {
    const result = await (
      await sc.getDB()
    )
      .select()
      // use event table just as a type mock
      .from(this.table as unknown as typeof gameTable)
      .where(eq(this.table[column], value))
      .limit(1);
    return transformNullToUndefined(result[0]) as Entity; // Return the first result or undefined
  }

  // Find all
  async list(
    filter?: {
      [P in keyof Entity]?: Entity[P] | null;
    },
    page?: { limit?: number; offset?: number },
  ): Promise<Entity[]> {
    const conditions: SQL[] = [];
    for (const [k, v] of Object.entries(filter || {})) {
      if (v === null) {
        conditions.push(isNull(this.table[k]));
      } else if (v !== undefined) {
        conditions.push(eq(this.table[k], v));
      }
    }

    const db = await sc.getDB();
    const query = db
      .select()
      // use event table just as a type mock
      .from(this.table as unknown as typeof gameTable)
      .where(and(...conditions));

    if (page) addLimitAndOffsetToQuery(page, query);
    return transformNullToUndefined(await query) as Entity[];
  }

  // Create a new record
  async create(data: InferInsertModel<T>): Promise<Entity> {
    if ("createdAt" in data) {
      (data as { createdAt?: Date }).createdAt = new Date();
    }
    if ("updatedAt" in data) {
      (data as { updatedAt?: Date }).updatedAt = new Date();
    }

    try {
      const result = await (await sc.getDB())
        .insert(this.table)
        .values(data)
        .returning();
      return result[0] as Entity; // Returning the inserted record
    } catch (e) {
      throw new AppError(
        {
          code: "FailedInsert",
          message: "No se pudo guardar",
          logLevel: "ERROR",
          statusCode: "INTERNAL_SERVER_ERROR",
        },
        e instanceof Error ? e : undefined,
      );
    }
  }

  // Update a record by id
  async update(id: K, data: Partial<InferInsertModel<T>>): Promise<Entity> {
    const result = await (
      await sc.getDB()
    )
      // use event table just as a type mock
      .update(this.table as unknown as typeof gameTable)
      .set(data)
      .where(eq(this.tableId, id))
      .returning();
    return transformNullToUndefined(result[0]) as Entity;
  }

  async delete(id: K): Promise<K> {
    const deleted: { id: K }[] = await (await sc.getDB())
      .delete(this.table)
      .where(eq(this.tableId, id))
      .returning({ id: this.tableId });
    return deleted[0].id;
  }

  get tableId() {
    return this.table.id;
  }
}
