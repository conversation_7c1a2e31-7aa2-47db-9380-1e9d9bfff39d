import { Middleware } from "@koa/router";
import { AppError } from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { gameService } from "../service";

export const getGame: Middleware<
  unknown,
  unknown,
  pchujoy.game.GetGameResponse
> = async ({ request, response }) => {
  const params = pchujoy.game.GetGameRequestSchema.parse(request.query);
  const entity = await gameService.get("id", params.id);

  if (!entity) {
    throw new AppError({
      code: "InfluencerNotFound",
      message: "No se encontró al influencer solicitado",
      statusCode: "NOT_FOUND",
    });
  }

  response.set("Cache-Control", "s-maxage=1800");
  response.body = pchujoy.game.GetGameResponseSchema.parse(entity);
};
