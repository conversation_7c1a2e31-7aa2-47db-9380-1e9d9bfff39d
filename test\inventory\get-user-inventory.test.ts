import request from "supertest";
import { app } from "../../src/api-handler";
import { accessoryService } from "../../src/accessory";
import { inventoryService } from "../../src/inventory";
import { TestData, userAuthToken } from "../common";
import { sc } from "../../src/services";
import { influencerTable, userTable } from "../../src/core-schema";

describe("get user inventory tests", () => {
  test("gets user inventory", async () => {
    // Create dependencies first
    const db = await sc.getDB();
    await db.insert(influencerTable).values(TestData.influencer);
    await db.insert(userTable).values(TestData.user);

    // Create accessories
    const accessory1 = await accessoryService.create(TestData.accessoryFace);
    const accessory2 = await accessoryService.create(TestData.accessoryHat);

    // Add accessories to user inventory
    await inventoryService.create({
      accessoryId: accessory1.id,
      userId: TestData.user.id,
    });
    await inventoryService.create({
      accessoryId: accessory2.id,
      userId: TestData.user.id,
    });

    const response = await request(app)
      .get("/live/inventory")
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toHaveLength(2);

    // Check first inventory item
    expect(response.body[0]).toEqual({
      id: accessory1.id,
      name: accessory1.name,
      type: accessory1.type,
      value: accessory1.value,
    });

    // Check second inventory item
    expect(response.body[1]).toEqual({
      id: accessory2.id,
      name: accessory2.name,
      type: accessory2.type,
      value: accessory2.value,
    });
  });

  test("returns empty array when user has no inventory", async () => {
    // Create dependencies first
    const db = await sc.getDB();
    await db.insert(influencerTable).values(TestData.influencer);
    await db.insert(userTable).values(TestData.user);

    const response = await request(app)
      .get("/live/inventory")
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toEqual([]);
  });

  test("returns 401 when no authentication token is provided", async () => {
    const response = await request(app).get("/live/inventory");

    expect(response.status).toBe(401);
    expect(response.body.code).toBe("BadAuthorization");
  });
});
