import { Middleware } from "@koa/router";
import {
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { inventoryService } from "../service";
import { pchujoy } from "@mainframe-peru/types";

export const getUserInventory: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  pchujoy.seasonPass.GetUserInventoryResponse
> = async ({ response, state }) => {
  const items = await inventoryService.getInventory(state.auth.id);
  response.set("Cache-Control", "s-maxage=1800");
  response.body = items;
};
