import { Middleware } from "@koa/router";
import { AppError } from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { accessoryService } from "../service";

export const getAccessory: Middleware<
  unknown,
  unknown,
  pchujoy.accessory.GetAccessoryResponse
> = async ({ request, response }) => {
  const params = pchujoy.accessory.GetAccessoryRequestSchema.parse(
    request.query,
  );
  const entity = await accessoryService.get("id", params.id);

  if (!entity) {
    throw new AppError({
      code: "AccessoryNotFound",
      message: "No se encontró el accesorio solicitado",
      statusCode: "NOT_FOUND",
    });
  }

  response.set("Cache-Control", "s-maxage=1800");
  response.body = pchujoy.accessory.GetAccessoryResponseSchema.parse(entity);
};
