import { PublishCommand } from "@aws-sdk/client-sns";
import supertest from "supertest";
import { app } from "../../src/api-handler";
import { influencerTable, userTable } from "../../src/core-schema";
import { seasonPassService } from "../../src/season-pass";
import { seasonPassLevelService } from "../../src/season-pass-level";
import { seasonPassUserProgressService } from "../../src/season-pass-user-progress";
import { inventoryService } from "../../src/inventory";
import { accessoryService } from "../../src/accessory";
import { sc } from "../../src/services";
import { TestData, userAuthToken } from "../common";

const request = supertest;

describe("POST /live/season-pass-user-progress/claim", () => {
  let snsMock: jest.Mock;

  beforeEach(async () => {
    snsMock = jest.fn();
    sc.sns.send = snsMock;

    const db = await sc.getDB();
    await db.insert(influencerTable).values(TestData.influencer);
    await db.insert(userTable).values(TestData.user);
    await seasonPassService.create(TestData.seasonPass);

  });

  describe("Success cases", () => {
    it("should successfully claim ACCESSORY reward", async () => {
      
      // Create base level first (required for user progress)
      const baseLevel = await seasonPassLevelService.create(TestData.seasonPassLevel);

      // Create accessory for the reward
      const accessory = await accessoryService.create(TestData.accessoryBackground);

      // Create level with ACCESSORY reward
      const level = await seasonPassLevelService.create({
        ...TestData.seasonPassLevel,
        id: undefined,
        levelIndex: 1,
        rewards: [
          {
            id: accessory.id,
            type: "ACCESSORY",
            content: {
              id: accessory.id,
              name: accessory.name,
              type: accessory.type,
              value: accessory.value,
            },
          },
        ],
      });
      
      await seasonPassUserProgressService.create({
        ...TestData.seasonPassUserProgress,
        currentLevel: level.id,
        levelsClaimed: [],
      });
      
      const requestBody = {
        userId: TestData.user.id,
        levelId: level.id,
      };

      await request(app)
        .post("/live/season-pass-user-progress/claim")
        .send(requestBody)
        .set("Cookie", `session=${await userAuthToken}`)
        .expect(204);

      // Verify the accessory was added to inventory
      const inventory = await inventoryService.getInventory(TestData.user.id);
      expect(inventory).toHaveLength(1);
      expect(inventory[0]).toMatchObject({
        id: accessory.id,
        name: accessory.name,
        type: accessory.type,
        value: accessory.value,
      });

      // Verify the level was marked as claimed
      const updatedProgress = await seasonPassUserProgressService.get(
        "userId",
        TestData.user.id,
      );
      expect(updatedProgress?.levelsClaimed).toContain(level.id);

      // Verify no SNS message was sent for ACCESSORY reward
      expect(snsMock).not.toHaveBeenCalled();
    });
  });
});