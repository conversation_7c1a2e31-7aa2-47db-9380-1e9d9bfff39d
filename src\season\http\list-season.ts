import { Middleware } from "@koa/router";
import { pchujoy } from "@mainframe-peru/types";
import { seasonService } from "../service";

export const listSeason: Middleware<
  unknown,
  unknown,
  pchujoy.season.ListSeasonsResponse
> = async ({ request, response }) => {
  const body = pchujoy.season.ListSeasonsRequestSchema.parse(request.query);
  const entities = await seasonService.list({
    startDate: body.startDate,
    endDate: body.endDate,
  });

  response.set("Cache-Control", "s-maxage=1800");
  response.body = entities.map((e) => ({
    id: e.id,
    name: e.name,
    startDate: e.startDate,
    endDate: e.endDate,
    createdAt: e.createdAt,
    updatedAt: e.updatedAt,
  }));
};
