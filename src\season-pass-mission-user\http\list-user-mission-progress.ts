import { Middleware } from "@koa/router";
import {
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { seasonPassService } from "../../season-pass";
import { seasonPassMissionUserService } from "../service";
import { pchujoy } from "@mainframe-peru/types";

export const listUserMissionProgress: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  pchujoy.seasonPass.ListUserMissionProgressResponse
> = async ({ response, state }) => {
  const currentSeasonPass = await seasonPassService.getCurrentSeasonPass();

  const userMissions =
    await seasonPassMissionUserService.getUserMissionsForSeasonPass(
      state.auth.id,
      currentSeasonPass.id,
    );

  const progressList = userMissions.map((mission) => ({
    missionId: mission.seasonPassMissionId,
    progressCount: mission.progressCount,
    completed: mission.completed,
    createdAt: mission.createdAt,
    updatedAt: mission.updatedAt,
  }));

  response.body = {
    missions: progressList,
  };
};
