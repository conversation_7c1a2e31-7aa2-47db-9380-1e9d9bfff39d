import { Middleware } from "@koa/router";
import {
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { transformNullToUndefined } from "../../common";
import { profileService } from "../service";

export const getProfile: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  pchujoy.profile.GetProfileResponse
> = async ({ response, state }) => {
  const entity = await profileService.getCurrentProfile(state.auth.id);
  response.body = pchujoy.profile.GetProfileResponseSchema.parse(
    transformNullToUndefined(entity),
  );
};
