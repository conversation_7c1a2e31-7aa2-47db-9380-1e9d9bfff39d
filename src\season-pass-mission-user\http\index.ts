import Router from "@koa/router";
import { getClientAuthMiddleware } from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { listUserMissionProgress } from "./list-user-mission-progress";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

router.get("/progress", authMiddleware, listUserMissionProgress);

export { router as seasonPassMissionUserRouter };
