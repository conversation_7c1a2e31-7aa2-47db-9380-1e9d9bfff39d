import request from "supertest";
import { app } from "../../src/api-handler";
import { seasonPassService } from "../../src/season-pass";
import { seasonPassLevelService } from "../../src/season-pass-level";
import { TestData } from "../common";

describe("list current season pass levels tests", () => {
  beforeEach(() => {
    // Clear cache before each test (database is automatically rolled back)
    seasonPassService.clearCache();
  });

  test("gets the current season pass levels ordered by level index", async () => {
    // Create a season pass
    const createdSeasonPass = await seasonPassService.create(
      TestData.seasonPass,
    );

    // Create some season pass levels
    const level1 = await seasonPassLevelService.create({
      seasonPassId: createdSeasonPass.id,
      levelIndex: 1,
      name: "Level 1",
      description: "First level",
      rewards: TestData.rewards,
      levelExp: 100,
    });

    const level3 = await seasonPassLevelService.create({
      seasonPassId: createdSeasonPass.id,
      levelIndex: 3,
      name: "Level 3",
      description: "Third level",
      rewards: TestData.rewards,
      levelExp: 300,
    });

    const level2 = await seasonPassLevelService.create({
      seasonPassId: createdSeasonPass.id,
      levelIndex: 2,
      name: "Level 2",
      description: "Second level",
      rewards: TestData.rewards,
      levelExp: 200,
    });

    const response = await request(app).get("/live/season-pass-level/current");

    expect(response.status).toBe(200);
    expect(response.body).toHaveLength(3);

    // Should be ordered by levelIndex
    expect(response.body[0]).toEqual({
      id: level1.id,
      levelIndex: 1,
      name: "Level 1",
      description: "First level",
      rewards: TestData.rewards,
      levelExp: 100,
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    });

    expect(response.body[1]).toEqual({
      id: level2.id,
      levelIndex: 2,
      name: "Level 2",
      description: "Second level",
      rewards: TestData.rewards,
      levelExp: 200,
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    });

    expect(response.body[2]).toEqual({
      id: level3.id,
      levelIndex: 3,
      name: "Level 3",
      description: "Third level",
      rewards: TestData.rewards,
      levelExp: 300,
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    });
  });

  test("returns empty array when no levels exist for current season pass", async () => {
    // Create a season pass but no levels
    await seasonPassService.create(TestData.seasonPass);

    const response = await request(app).get("/live/season-pass-level/current");

    expect(response.status).toBe(200);
    expect(response.body).toEqual([]);
  });

  test("returns 404 when no active season pass exists", async () => {
    // Create a season pass that has already ended
    const expiredSeasonPass = {
      name: "Expired Season Pass",
      bannerUrl: "https://example.com/expired-banner.png",
      startDate: "2020-01-01",
      endDate: "2020-12-31",
    };

    const createdExpiredSeasonPass =
      await seasonPassService.create(expiredSeasonPass);

    // Create levels for the expired season pass
    await seasonPassLevelService.create({
      seasonPassId: createdExpiredSeasonPass.id,
      levelIndex: 1,
      name: "Expired Level",
      description: "This level is from an expired season pass",
      rewards: TestData.rewards,
      levelExp: 100,
    });

    const response = await request(app).get("/live/season-pass-level/current");

    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      code: "NoActiveSeasonPass",
      message: "There is no active season pass",
    });
  });

  test("returns levels only for the current active season pass", async () => {
    // Create an expired season pass with levels
    const expiredSeasonPass = {
      name: "Expired Season Pass",
      bannerUrl: "https://example.com/expired-banner.png",
      startDate: "2020-01-01",
      endDate: "2020-12-31",
    };
    const createdExpiredSeasonPass =
      await seasonPassService.create(expiredSeasonPass);

    await seasonPassLevelService.create({
      seasonPassId: createdExpiredSeasonPass.id,
      levelIndex: 1,
      name: "Expired Level",
      description: "This should not appear",
      rewards: TestData.rewards,
      levelExp: 100,
    });

    // Create current active season pass with levels
    const createdCurrentSeasonPass = await seasonPassService.create(
      TestData.seasonPass,
    );

    const currentLevel = await seasonPassLevelService.create({
      seasonPassId: createdCurrentSeasonPass.id,
      levelIndex: 1,
      name: "Current Level",
      description: "This should appear",
      rewards: TestData.rewards,
      levelExp: 200,
    });

    const response = await request(app).get("/live/season-pass-level/current");

    expect(response.status).toBe(200);
    expect(response.body).toHaveLength(1);
    expect(response.body[0]).toEqual({
      id: currentLevel.id,
      levelIndex: 1,
      name: "Current Level",
      description: "This should appear",
      rewards: TestData.rewards,
      levelExp: 200,
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    });
  });

  test("sets proper cache headers", async () => {
    await seasonPassService.create(TestData.seasonPass);

    const response = await request(app).get("/live/season-pass-level/current");

    expect(response.status).toBe(200);
    expect(response.headers["cache-control"]).toBe("s-maxage=1800");
  });
});
