import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { accessoryService } from "../service";

export const deleteAccessory: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  null
> = async ({ request, response }) => {
  const { id } = pchujoy.accessory.DeleteAccessoryRequestSchema.parse(
    request.query,
  );
  await accessoryService.delete(id);

  response.body = null;
};
