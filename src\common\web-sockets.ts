import { webSockets } from "@mainframe-peru/types";
import { sc } from "../services";
import { PublishCommand, PublishCommandOutput } from "@aws-sdk/client-sns";
import { logger } from "@mainframe-peru/common-core";

export const webSocketsQueue = {
  async sendMessage(
    request: webSockets.SendUserMessage,
  ): Promise<PublishCommandOutput | undefined> {
    try {
      const command = new PublishCommand({
        TopicArn: sc.vars.snsTopicArn,
        Message: JSON.stringify(request),
        MessageAttributes: {
          service: {
            DataType: "String",
            StringValue: "web-sockets",
          },
        },
      });
      return await sc.sns.send(command);
    } catch (e) {
      logger.error("Error sending message to sqs email queue", e);
    }
  },
};
