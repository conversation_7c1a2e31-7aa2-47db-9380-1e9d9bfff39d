import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { gameService } from "../service";

export const updateGame: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  pchujoy.game.UpdateGameResponse
> = async ({ request, response }) => {
  const { id, ...update } = pchujoy.game.UpdateGameRequestSchema.parse(
    request.body,
  );
  const updatedGame = await gameService.update(id, update);

  response.body = {
    id: updatedGame.id,
    createdAt: updatedGame.createdAt,
    description: updatedGame.description,
    imageUrl: updatedGame.imageUrl,
    name: updatedGame.name,
    sourceUrl: updatedGame.sourceUrl,
    status: updatedGame.status,
    updatedAt: updatedGame.updatedAt,
  };
};
