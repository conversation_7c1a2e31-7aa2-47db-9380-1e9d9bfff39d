AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: MS for saving modifiable pdf documents for contract creators
Parameters:
  ServiceName:
    Type: String
    Default: pchujoy-backend

  EnvName:
    Type: AWS::SSM::Parameter::Value<String>
    Description: The environment name.
    Default: /env/name

Conditions:
  IsDev: !Equals [!Ref EnvName, "dev"]
  IsStg: !Equals [!Ref EnvName, "stg"]
  IsProd: !Equals [!Ref EnvName, "prod"]
  PrefixResources: !Or
    - !Equals [!Ref EnvName, "dev"]
    - !Equals [!Ref EnvName, "stg"]

Resources:
  #----------------------------------------------------------------
  # Lambda Role
  #----------------------------------------------------------------
  LambdaFunctionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${ServiceName}-lambdafnc-role
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action:
              - sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
        - arn:aws:iam::aws:policy/service-role/AWSLambdaENIManagementAccess

  # Main policy, separated from the role so that they
  # can use dependant items without making loops
  LambdaFunctionRolePolicy:
    Type: AWS::IAM::Policy
    Properties:
      PolicyName: MainPolicy
      Roles:
        - !Ref LambdaFunctionRole
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Sid: AccessRDSSecret
            Effect: Allow
            Action:
              - secretsmanager:GetSecretValue
            Resource:
              - !ImportValue RDS-SECRET
          - Sid: AccessRDSSecretKMS
            Effect: Allow
            Action:
              - kms:Decrypt
            Resource:
              - !ImportValue RDS-SECRET-KMS-KEY
          - Sid: AllowPushToSNS
            Effect: Allow
            Action: sns:Publish
            Resource:
              - !ImportValue MAIN-SNS-EVENT-BUS-ARN
          - Sid: AccessSQS
            Effect: Allow
            Action:
              - sqs:ReceiveMessage
              - sqs:DeleteMessage
              - sqs:Send*
              - sqs:GetQueueAttributes
            Resource:
              - !GetAtt SQSBusEventsQueue.Arn
              - !GetAtt SQSBusEventsDeadLetterQueue.Arn

  LambdaSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: !Sub ${ServiceName} lambda security group
      VpcId: !ImportValue MAIN-VPC
      SecurityGroupIngress:
        - IpProtocol: tcp
          CidrIp: 10.0.0.0/16
          FromPort: 0
          ToPort: 65535

  #----------------------------------------------------------------
  # SQS Processing of messages from the Main SNS Bus Event
  #----------------------------------------------------------------
  SQSBusEventsDeadLetterQueue:
    Type: AWS::SQS::Queue

  SQSBusEventsDeadLetterQueuePolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - !Ref SQSBusEventsDeadLetterQueue
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Sid: AllowLambdaToSendMessages
            Effect: Allow
            Principal:
              AWS: "*"
            Action: sqs:SendMessage
            Resource: !GetAtt SQSBusEventsDeadLetterQueue.Arn
            Condition:
              ArnEquals:
                "aws:SourceArn": !GetAtt LambdaFunctionRole.Arn

  SQSBusEventsQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub ${ServiceName}-queue
      VisibilityTimeout: 1500
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt SQSBusEventsDeadLetterQueue.Arn
        maxReceiveCount: 5

  # Subscribe the SQS to the event bus SNS
  SQSBusEventsSubscriptionToSNSBus:
    Type: AWS::SNS::Subscription
    Properties:
      TopicArn: !ImportValue MAIN-SNS-EVENT-BUS-ARN
      Protocol: sqs
      Endpoint: !GetAtt SQSBusEventsQueue.Arn
      RawMessageDelivery: true
      FilterPolicy:
        service:
          - !Ref ServiceName

  SQSBusEventsQueuePolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - !Ref SQSBusEventsQueue
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Sid: AllowLambdaToSendMessages
            Effect: Allow
            Principal:
              AWS: "*"
            Action: sqs:SendMessage
            Resource: !GetAtt SQSBusEventsQueue.Arn
            Condition:
              ArnEquals:
                "aws:SourceArn": !GetAtt LambdaFunctionRole.Arn
          - Sid: AllowSNSToSendMessages
            Effect: Allow
            Principal:
              AWS: "*"
            Action: sqs:SendMessage
            Resource: !GetAtt SQSBusEventsQueue.Arn
            Condition:
              ArnEquals:
                "aws:SourceArn": !ImportValue MAIN-SNS-EVENT-BUS-ARN

  LambdaSQSFunction:
    Type: AWS::Serverless::Function
    DependsOn: LambdaFunctionRolePolicy
    Properties:
      FunctionName: !Sub ${ServiceName}-sqs
      Role: !GetAtt LambdaFunctionRole.Arn
      Runtime: nodejs20.x
      Timeout: 500
      MemorySize: 128
      AutoPublishAlias: live
      Tracing: Active
      Layers:
        - !Sub arn:aws:lambda:${AWS::Region}:580247275435:layer:LambdaInsightsExtension:2
      DeploymentPreference:
        Type: AllAtOnce
      CodeUri: ./build
      Handler: sqs-handler.handler
      Environment:
        Variables:
          ENV_NAME: !Ref EnvName
          DB_ENDPOINT: !ImportValue RDS-ENDPOINT
          DB_SECRET: !ImportValue RDS-SECRET
          SNS_TOPIC_ARN: !ImportValue MAIN-SNS-EVENT-BUS-ARN
          NODE_EXTRA_CA_CERTS: /var/task/us-east-1-bundle.pem
      VpcConfig:
        SecurityGroupIds:
          - !Ref LambdaSecurityGroup
        SubnetIds:
          - !ImportValue PRIVATE-SUBNET-A
          - !ImportValue PRIVATE-SUBNET-B
      Events:
        SQS:
          Type: SQS
          Properties:
            Queue: !GetAtt SQSBusEventsQueue.Arn
            BatchSize: 10
            FunctionResponseTypes:
              - ReportBatchItemFailures

  #----------------------------------------------------------------
  # API Gateway processing of events
  #----------------------------------------------------------------
  ApiGateway:
    Type: AWS::Serverless::HttpApi
    Properties:
      Name: !Ref ServiceName
      StageName: live

  # The lambda function that will process Api Gateway requests
  LambdaApiFunction:
    Type: AWS::Serverless::Function
    DependsOn: LambdaFunctionRolePolicy
    Properties:
      FunctionName: !Sub ${ServiceName}-api
      Role: !GetAtt LambdaFunctionRole.Arn
      Runtime: nodejs20.x
      Timeout: 27
      MemorySize: 384
      Tracing: Active
      Layers:
        - !Sub arn:aws:lambda:${AWS::Region}:580247275435:layer:LambdaInsightsExtension:2
      AutoPublishAlias: live
      DeploymentPreference:
        Type: AllAtOnce
      CodeUri: ./build
      Handler: api-handler.handler
      VpcConfig:
        SecurityGroupIds:
          - !Ref LambdaSecurityGroup
        SubnetIds:
          - !ImportValue PRIVATE-SUBNET-A
          - !ImportValue PRIVATE-SUBNET-B
      Environment:
        Variables:
          ENV_NAME: !Ref EnvName
          DB_ENDPOINT: !ImportValue RDS-ENDPOINT
          DB_SECRET: !ImportValue RDS-SECRET
          SNS_TOPIC_ARN: !ImportValue MAIN-SNS-EVENT-BUS-ARN
          NODE_EXTRA_CA_CERTS: /var/task/us-east-1-bundle.pem
      Events:
        DefaultEventSource:
          Type: HttpApi
          Properties:
            ApiId: !Ref ApiGateway

  ApiGatewayApiMapping:
    Type: AWS::ApiGatewayV2::ApiMapping
    DependsOn: ApiGatewayliveStage
    Properties:
      DomainName: !ImportValue MAIN-API-DOMAIN-NAME
      ApiId: !Ref ApiGateway
      ApiMappingKey: api/pchujoy
      Stage: live

  LogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/lambda/${ServiceName}-api
