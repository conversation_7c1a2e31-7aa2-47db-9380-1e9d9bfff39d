import { pchujoy } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { gameService } from "../../src/game";
import { gameScoreService } from "../../src/game-score";
import { TestData, userAuthToken } from "../common";
import { gameSeasonService } from "../../src/game-season";
import { seasonService } from "../../src/season";
import { sc } from "../../src/services";
import { influencerTable, userTable } from "../../src/core-schema";

describe("create game score tests", () => {
  test("create a game score", async () => {
    const db = await sc.getDB();
    await db.insert(influencerTable).values(TestData.influencer);
    await db.insert(userTable).values(TestData.user);
    await gameService.create(TestData.game);
    await seasonService.create({
      ...TestData.season,
      startDate: "2025-01-01",
      endDate: "2030-01-01",
    });
    await gameSeasonService.create({
      gameId: TestData.game.id,
      seasonId: TestData.season.id,
    });
    const requestBody: pchujoy.gameScore.CreateGameScoreRequest = {
      gameId: TestData.game.id,
      score: 100,
    };

    const response = await request(app)
      .post("/live/game-score")
      .send(requestBody)
      .set("Cookie", `session=${await userAuthToken}`);

    const gameScore = await gameScoreService.get("gameId", TestData.game.id);
    expect(response.status).toBe(200);
    expect(gameScore?.score).toEqual(100);
  });
});
