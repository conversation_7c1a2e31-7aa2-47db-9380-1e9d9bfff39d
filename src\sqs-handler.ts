import serverlessExpress from "@codegenie/serverless-express";
import { bodyParser } from "@koa/bodyparser";
import Router from "@koa/router";
import {
  buildSQSHandler,
  errorMiddleware,
  eventSource,
  loggerMiddleware,
} from "@mainframe-peru/common-core";
import Koa from "koa";
import { seasonPassMissionSqsRouter } from "./season-pass-mission";

const koa = new Koa();
const router = new Router();

/**
 * Middlewares
 */
koa.use(errorMiddleware);
koa.use(bodyParser());
koa.use(loggerMiddleware);

/**
 * Endpoints
 */
router.use("/season-pass-mission", seasonPassMissionSqsRouter.routes());

/**
 * Finish setup
 */
koa.use(router.routes());

export const app = koa.callback();
export const handler = buildSQSHandler(
  serverlessExpress({
    app,
    eventSource,
    eventSourceRoutes: {
      AWS_SQS: "/",
    },
  }),
);
