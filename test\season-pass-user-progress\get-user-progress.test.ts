import supertest from "supertest";
import { app } from "../../src/api-handler";
import { influencerTable, userTable } from "../../src/core-schema";
import { seasonPassService } from "../../src/season-pass";
import { seasonPassLevelService } from "../../src/season-pass-level";
import { seasonPassUserProgressService } from "../../src/season-pass-user-progress";
import { sc } from "../../src/services";
import { TestData, userAuthToken } from "../common";

const request = supertest;

describe("GET /live/season-pass-user-progress/progress", () => {
  it("should return user progress for current season pass", async () => {
    const db = await sc.getDB();
    await db.insert(influencerTable).values(TestData.influencer);
    await db.insert(userTable).values(TestData.user);
    await seasonPassService.create(TestData.seasonPass);
    await seasonPassLevelService.create(TestData.seasonPassLevel);
    await seasonPassUserProgressService.create({
      ...TestData.seasonPassUserProgress,
      currentExp: 123,
    });

    const response = await request(app)
      .get("/live/season-pass-user-progress/progress")
      .set("Cookie", `session=${await userAuthToken}`)
      .expect(200);

    expect(response.body).toEqual({
      currentExp: 123,
      currentLevel: {
        description: "description",
        name: "Test season pass level",
      },
    });
  });

  it("should create user progress when user progress not found", async () => {
    const db = await sc.getDB();
    await db.insert(influencerTable).values(TestData.influencer);
    await db.insert(userTable).values(TestData.user);
    await seasonPassService.create(TestData.seasonPass);
    await seasonPassLevelService.create(TestData.seasonPassLevel);

    const response = await request(app)
      .get("/live/season-pass-user-progress/progress")
      .set("Cookie", `session=${await userAuthToken}`)
      .expect(200);

    expect(response.body).toEqual({
      currentExp: 0,
      currentLevel: {
        description: "description",
        name: "Test season pass level",
      },
    });
  });

  it("should return 401 when no authentication provided", async () => {
    await request(app)
      .get("/live/season-pass-user-progress/progress")
      .expect(401);
  });
});
