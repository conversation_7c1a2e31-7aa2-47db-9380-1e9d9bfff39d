import { ServiceBase } from "../service-base";
import { InventoryEntity, inventoryRepository } from "./repository";

class InventoryService extends ServiceBase<
  typeof inventoryRepository,
  InventoryEntity
> {
  constructor() {
    super(inventoryRepository);
  }
  async getInventory(userId: number) {
    return await this.repository.getInventory(userId);
  }
}

export const inventoryService = new InventoryService();
