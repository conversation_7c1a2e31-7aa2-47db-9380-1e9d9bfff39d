import request from "supertest";
import { app } from "../../src/api-handler";
import { accessoryService } from "../../src/accessory";
import { TestData, userAuthToken } from "../common";

describe("list accessory tests", () => {
  beforeEach(async () => {
    // Create multiple accessories for testing
    await accessoryService.create(TestData.accessoryGlasses); // glasses
    await accessoryService.create(TestData.accessoryHat); // hat
    await accessoryService.create(TestData.accessoryFace); // face

    // Create additional accessories for pagination testing
    await accessoryService.create({
      name: "Another Hat",
      type: "hat",
      value: "https://example.com/another-hat.png",
    });
    await accessoryService.create({
      name: "Background 1",
      type: "background",
      value: "https://example.com/bg1.png",
    });
  });

  test("lists all accessories without filters", async () => {
    const response = await request(app)
      .get("/live/accessory/list")
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    expect(Array.isArray(response.body)).toBe(true);
    expect(response.body.length).toBeGreaterThanOrEqual(5);

    // Check that each item has the expected structure
    response.body.forEach((accessory: any) => {
      expect(accessory).toEqual({
        id: expect.any(Number),
        name: expect.any(String),
        type: expect.stringMatching(
          /^(face|hat|pet|glasses|title|background)$/,
        ),
        value: expect.any(String),
        createdAt: expect.any(String),
        updatedAt: expect.any(String),
      });
    });
  });

  test("filters accessories by type", async () => {
    const queryParams = {
      type: "hat",
    };

    const response = await request(app)
      .get("/live/accessory/list")
      .query(queryParams)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    expect(Array.isArray(response.body)).toBe(true);
    expect(response.body.length).toBe(2); // Should have 2 hat accessories

    response.body.forEach((accessory: any) => {
      expect(accessory.type).toBe("hat");
    });
  });

  test("filters accessories by different types", async () => {
    const types = ["face", "glasses", "background"];

    for (const type of types) {
      const response = await request(app)
        .get("/live/accessory/list")
        .query({ type })
        .set("Cookie", `session=${await userAuthToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);

      response.body.forEach((accessory: any) => {
        expect(accessory.type).toBe(type);
      });
    }
  });

  test("supports pagination with limit", async () => {
    const queryParams = {
      limit: 2,
    };

    const response = await request(app)
      .get("/live/accessory/list")
      .query(queryParams)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    expect(Array.isArray(response.body)).toBe(true);
    expect(response.body.length).toBe(2);
  });

  test("supports pagination with offset", async () => {
    // Get first page
    const firstPageResponse = await request(app)
      .get("/live/accessory/list")
      .query({ limit: 2, offset: 0 })
      .set("Cookie", `session=${await userAuthToken}`);

    // Get second page
    const secondPageResponse = await request(app)
      .get("/live/accessory/list")
      .query({ limit: 2, offset: 2 })
      .set("Cookie", `session=${await userAuthToken}`);

    expect(firstPageResponse.status).toBe(200);
    expect(secondPageResponse.status).toBe(200);

    // Ensure different results
    const firstPageIds = firstPageResponse.body.map((a: any) => a.id);
    const secondPageIds = secondPageResponse.body.map((a: any) => a.id);

    expect(firstPageIds).not.toEqual(secondPageIds);
  });

  test("combines type filter with pagination", async () => {
    const queryParams = {
      type: "hat",
      limit: 1,
      offset: 0,
    };

    const response = await request(app)
      .get("/live/accessory/list")
      .query(queryParams)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    expect(Array.isArray(response.body)).toBe(true);
    expect(response.body.length).toBe(1);
    expect(response.body[0].type).toBe("hat");
  });

  test("returns empty array when no accessories match filter", async () => {
    const queryParams = {
      type: "pet", // No pet accessories were created
    };

    const response = await request(app)
      .get("/live/accessory/list")
      .query(queryParams)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    expect(Array.isArray(response.body)).toBe(true);
    expect(response.body.length).toBe(0);
  });

  test("validates limit parameter bounds", async () => {
    // Test limit too high
    const highLimitResponse = await request(app)
      .get("/live/accessory/list")
      .query({ limit: 101 }) // Max is 100
      .set("Cookie", `session=${await userAuthToken}`);

    expect(highLimitResponse.status).toBe(400);

    // Test limit too low
    const lowLimitResponse = await request(app)
      .get("/live/accessory/list")
      .query({ limit: 0 }) // Min is 1
      .set("Cookie", `session=${await userAuthToken}`);

    expect(lowLimitResponse.status).toBe(400);
  });

  test("validates offset parameter", async () => {
    const negativeOffsetResponse = await request(app)
      .get("/live/accessory/list")
      .query({ offset: -1 }) // Min is 0
      .set("Cookie", `session=${await userAuthToken}`);

    expect(negativeOffsetResponse.status).toBe(400);
  });

  test("returns 400 when type is invalid", async () => {
    const queryParams = {
      type: "invalid_type",
    };

    const response = await request(app)
      .get("/live/accessory/list")
      .query(queryParams)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(400);
  });

  test("sets cache control header", async () => {
    const response = await request(app)
      .get("/live/accessory/list")
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);
    expect(response.headers["cache-control"]).toBe("s-maxage=1800");
  });
});
