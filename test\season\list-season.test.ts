import { pchujoy } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { seasonService } from "../../src/season";
import { TestData } from "../common";

describe("list season tests", () => {
  beforeEach(async () => {
    for (let i = 0; i < 3; i++) {
      await seasonService.create({
        ...TestData.season,
        id: i,
      });
    }
  });

  test("list an existing active seasons", async () => {
    const queryParams: pchujoy.season.ListSeasonsRequest = {};

    const response = await request(app)
      .get("/live/season/list")
      .query(queryParams);

    expect(response.status).toBe(200);
    expect(response.body.length).toEqual(3);
  });
});
