import { webSocketsQueue } from "../common/web-sockets";
import { seasonPassLevelService } from "../season-pass-level";
import { SeasonPassEntity } from "../season-pass/repository";
import { ServiceBase } from "../service-base";
import {
  SeasonPassUserProgressEntity,
  seasonPassUserProgressRepository,
} from "./repository";

class SeasonPassUserProgressService extends ServiceBase<
  typeof seasonPassUserProgressRepository,
  SeasonPassUserProgressEntity
> {
  constructor() {
    super(seasonPassUserProgressRepository);
  }

  async addExperience(
    userId: number,
    seasonPass: SeasonPassEntity,
    experience: number,
  ): Promise<void> {
    const progress = await this.getUserProgress(userId, seasonPass.id);

    const levels =
      await seasonPassLevelService.getCurrentSeasonPassLevels(seasonPass);
    let greatestLevelId = -1;
    let greatestLevelExp = -1;
    for (const l of levels) {
      if (
        progress.currentExp + experience > l.levelExp &&
        l.levelExp > greatestLevelExp
      ) {
        greatestLevelExp = l.levelExp;
        greatestLevelId = l.id;
      }
    }

    await this.repository.addExperience(
      userId,
      seasonPass.id,
      experience,
      greatestLevelId === -1 ? progress.currentLevel : greatestLevelId,
    );

    const upgradeLevel = levels.find((l) => l.id === greatestLevelId);
    if (upgradeLevel && upgradeLevel.id !== progress.currentLevel) {
      await webSocketsQueue.sendMessage({
        userId,
        message: {
          type: "season-pass-level-completed",
          levelExp: upgradeLevel.levelExp,
          rewards: upgradeLevel.rewards,
          name: upgradeLevel.name,
          description: upgradeLevel.description,
        },
      });
    }
  }

  async getUserProgress(
    userId: number,
    seasonPassId: number,
  ): Promise<SeasonPassUserProgressEntity> {
    const progress = await this.repository.getProgress(userId, seasonPassId);
    if (!progress) {
      const level = await seasonPassLevelService.getFirstLevel(seasonPassId);
      return await this.repository.create({
        currentLevel: level.id,
        seasonPassId,
        currentExp: 0,
        userId,
      });
    }
    return progress;
  }
}

export const seasonPassUserProgressService =
  new SeasonPassUserProgressService();
