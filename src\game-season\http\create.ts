import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { gameSeasonService } from "../service";

export const createGameSeason: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  pchujoy.gameSeason.CreateGameSeasonResponse
> = async ({ request, response }) => {
  const body = pchujoy.gameSeason.CreateGameSeasonRequestSchema.parse(
    request.body,
  );
  for (const i of body) {
    await gameSeasonService.create(i);
  }

  response.body = {
    amount: body.length,
  };
};
