import { defineConfig } from "drizzle-kit";

export default defineConfig({
  schema: "./src/schema.ts",
  out: "./drizzle",
  dialect: "postgresql", // 'postgresql' | 'mysql' | 'sqlite'
  dbCredentials: {
    //url: "postgresql://user:password@host:port/dbname"
    url: "postgresql://mainframe:not_the_password@localhost:5432/mainframe",
  },
  migrations: {
    table: "__drizzle_pchujoy_migrations",
    schema: "drizzle",
  },
});
