import { InferSelectModel, SQL, and, eq } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { gameSeasonTable, gameTable } from "../schema";
import { sc } from "../services";
import { pchujoy } from "@mainframe-peru/types";

export type GameEntity = InferSelectModel<typeof gameTable>;

type ListBySeasonInput = pchujoy.game.ListGamesRequest;

class GameRepository extends RepositoryBase<typeof gameTable, number> {
  constructor() {
    super(gameTable);
  }

  async complexList(params: ListBySeasonInput): Promise<GameEntity[]> {
    const db = await sc.getDB();

    const conditions: (SQL | undefined)[] = [];
    if (params.seasonId) {
      conditions.push(eq(gameSeasonTable.seasonId, params.seasonId));
    } else if (params.status) {
      conditions.push(eq(this.table.status, params.status));
    }

    const baseQuery = db.select().from(this.table);
    if (params.seasonId) {
      const result = await baseQuery
        .innerJoin(gameSeasonTable, eq(this.table.id, gameSeasonTable.gameId))
        .orderBy(gameSeasonTable.priority)
        .where(and(...conditions));
      return result.map((r) => r.game);
    }

    const result = await baseQuery.where(and(...conditions));
    return result;
  }
}

export const gameRepository = new GameRepository();
