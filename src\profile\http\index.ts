import Router from "@koa/router";
import { getClientAuthMiddleware } from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { getProfile } from "./get-profile";
import { updateProfile } from "./update-profile";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

// Profile
router.put("/", authMiddleware, updateProfile);
router.get("/", authMiddleware, getProfile);

export const profileRouter = router;
