{"id": "c7c552c0-5007-48c6-b379-95a328b74fbd", "prevId": "7efbc683-f461-4f95-b8af-4cbb2cc96571", "version": "7", "dialect": "postgresql", "tables": {"pchujoy.game_score": {"name": "game_score", "schema": "p<PERSON><PERSON>", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "season_id": {"name": "season_id", "type": "integer", "primaryKey": false, "notNull": true}, "game_id": {"name": "game_id", "type": "text", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"game_score_user_id_user_id_fk": {"name": "game_score_user_id_user_id_fk", "tableFrom": "game_score", "tableTo": "user", "schemaTo": "core_backend", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "game_score_season_id_season_id_fk": {"name": "game_score_season_id_season_id_fk", "tableFrom": "game_score", "tableTo": "season", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["season_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "game_score_game_id_game_id_fk": {"name": "game_score_game_id_game_id_fk", "tableFrom": "game_score", "tableTo": "game", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["game_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "pchujoy.game_season": {"name": "game_season", "schema": "p<PERSON><PERSON>", "columns": {"season_id": {"name": "season_id", "type": "integer", "primaryKey": false, "notNull": true}, "game_id": {"name": "game_id", "type": "text", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"game_season_season_id_season_id_fk": {"name": "game_season_season_id_season_id_fk", "tableFrom": "game_season", "tableTo": "season", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["season_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "game_season_game_id_game_id_fk": {"name": "game_season_game_id_game_id_fk", "tableFrom": "game_season", "tableTo": "game", "schemaTo": "p<PERSON><PERSON>", "columnsFrom": ["game_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"game_season_season_id_game_id_pk": {"name": "game_season_season_id_game_id_pk", "columns": ["season_id", "game_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "pchujoy.game": {"name": "game", "schema": "p<PERSON><PERSON>", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true}, "source_url": {"name": "source_url", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "game_status", "typeSchema": "p<PERSON><PERSON>", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "pchujoy.season": {"name": "season", "schema": "p<PERSON><PERSON>", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"pchujoy.game_status": {"name": "game_status", "schema": "p<PERSON><PERSON>", "values": ["ACTIVE", "INACTIVE"]}}, "schemas": {"pchujoy": "p<PERSON><PERSON>"}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}