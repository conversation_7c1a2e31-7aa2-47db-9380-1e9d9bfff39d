import { InferSelectModel, and, desc, eq, sql } from "drizzle-orm";
import { userTable } from "../core-schema";
import { RepositoryBase } from "../repository-base";
import { gameScoreTable, gameTable } from "../schema";
import { sc } from "../services";

export type GameScoreEntity = InferSelectModel<typeof gameScoreTable>;

type GetSeasonLeaderboardInput = {
  seasonId: number;
  gameId: string;
};

type GetSeasonLeaderboardOutput = {
  username: string;
  score: number;
}[];

type GetUserScoresInput = {
  seasonId: number;
  gameId: string;
  userId: number;
};

type GetUserScoresOutput = {
  rank: number;
  date: Date;
  score: number;
}[];

type GetUserTopScoresInput = {
  seasonId: number;
  userId: number;
};

type GetUserTopScoresOutput = {
  id: string;
  name: string;
  topScore: number;
}[];

class GameScoreRepository extends RepositoryBase<
  typeof gameScoreTable,
  number
> {
  constructor() {
    super(gameScoreTable);
  }

  async getSeasonLeaderboard(
    input: GetSeasonLeaderboardInput,
  ): Promise<GetSeasonLeaderboardOutput> {
    const db = await sc.getDB();
    return await db
      .select({
        score: this.table.score,
        username: userTable.alias,
      })
      .from(this.table)
      .innerJoin(userTable, eq(this.table.userId, userTable.id))
      .where(
        and(
          eq(this.table.gameId, input.gameId),
          eq(this.table.seasonId, input.seasonId),
        ),
      )
      .orderBy(desc(this.table.score))
      .limit(10);
  }

  async getUserScoresByGame(
    input: GetUserScoresInput,
  ): Promise<GetUserScoresOutput> {
    const db = await sc.getDB();
    const leaderBoardQuery = db
      .select({
        rank: sql<number>`RANK () OVER (ORDER BY ${this.table.score} DESC)`.as(
          "rank",
        ),
        userId: this.table.userId,
        score: this.table.score,
        date: this.table.createdAt,
      })
      .from(this.table)
      .where(
        and(
          eq(this.table.gameId, input.gameId),
          eq(this.table.seasonId, input.seasonId),
        ),
      )
      .as("sq");
    return await db
      .select({
        rank: sql<number>`${leaderBoardQuery.rank}::int`,
        score: leaderBoardQuery.score,
        date: leaderBoardQuery.date,
      })
      .from(leaderBoardQuery)
      .where(eq(leaderBoardQuery.userId, input.userId))
      .orderBy(desc(leaderBoardQuery.score))
      .limit(10);
  }

  async getUserTopScores(
    input: GetUserTopScoresInput,
  ): Promise<GetUserTopScoresOutput> {
    const db = await sc.getDB();
    const topScore = sql<number>`MAX(${this.table.score})`.as("top_score");
    return db
      .select({
        id: gameTable.id,
        name: gameTable.name,
        topScore,
      })
      .from(this.table)
      .innerJoin(gameTable, eq(this.table.gameId, gameTable.id))
      .where(
        and(
          eq(this.table.userId, input.userId),
          eq(this.table.seasonId, input.seasonId),
        ),
      )
      .groupBy(gameTable.id)
      .orderBy(desc(topScore));
  }
}

export const gameScoreRepository = new GameScoreRepository();
