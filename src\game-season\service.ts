import { ServiceBase } from "../service-base";
import { GameSeasonEntity, gameSeasonRepository } from "./repository";

class GameSeasonService extends ServiceBase<
  typeof gameSeasonRepository,
  GameSeasonEntity
> {
  constructor() {
    super(gameSeasonRepository);
  }

  deleteBySeasonAndGame: (typeof gameSeasonRepository)["deleteBySeasonAndGame"] =
    (seasonId, gameId) =>
      this.repository.deleteBySeasonAndGame(seasonId, gameId);
}

export const gameSeasonService = new GameSeasonService();
