import { and, eq, gte, InferSelectModel, lte, sql } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import {
  seasonPassMissionTable,
  missionPeriodEnum,
  missionTypeEnum,
} from "../schema";
import { sc } from "../services";
import { ActivityLogEvent } from "@mainframe-peru/types/build/common";

type ListCurrentMissionsInput = {
  action: ActivityLogEvent;
};

export type ListSeasonPassMissionsInput = {
  startAt?: Date;
  endAt?: Date;
  period?: (typeof missionPeriodEnum.enumValues)[number];
  seasonPassId?: number;
  isActive?: boolean;
  type?: (typeof missionTypeEnum.enumValues)[number];
};

export type SeasonPassMissionEntity = InferSelectModel<
  typeof seasonPassMissionTable
>;

class SeasonPassMissionRepository extends RepositoryBase<
  typeof seasonPassMissionTable,
  number
> {
  constructor() {
    super(seasonPassMissionTable);
  }

  async listCurrentMissions(
    input: ListCurrentMissionsInput,
  ): Promise<SeasonPassMissionEntity[]> {
    const db = await sc.getDB();
    return db
      .select()
      .from(this.table)
      .where(
        and(
          eq(this.table.action, input.action),
          lte(this.table.startAt, sql`now()`),
          gte(this.table.endAt, sql`now()`),
          eq(this.table.isActive, true),
        ),
      );
  }

  async list(
    input: ListSeasonPassMissionsInput,
  ): Promise<SeasonPassMissionEntity[]> {
    const db = await sc.getDB();
    const conditions = [];

    if (input.startAt) {
      conditions.push(gte(this.table.startAt, input.startAt));
    }
    if (input.endAt) {
      conditions.push(lte(this.table.endAt, input.endAt));
    }
    if (input.period) {
      conditions.push(eq(this.table.period, input.period));
    }
    if (input.seasonPassId) {
      conditions.push(eq(this.table.seasonPassId, input.seasonPassId));
    }
    if (input.isActive !== undefined) {
      conditions.push(eq(this.table.isActive, input.isActive));
    }
    if (input.type) {
      conditions.push(eq(this.table.type, input.type));
    }

    return db
      .select()
      .from(this.table)
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .orderBy(this.table.sortOrder, this.table.createdAt);
  }
}

export const seasonPassMissionRepository = new SeasonPassMissionRepository();
