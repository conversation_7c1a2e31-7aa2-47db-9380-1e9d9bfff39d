import { ServiceBase } from "../service-base";
import { GameScoreEntity, gameScoreRepository } from "./repository";

class GameScoreService extends ServiceBase<
  typeof gameScoreRepository,
  GameScoreEntity
> {
  constructor() {
    super(gameScoreRepository);
  }

  getSeasonLeaderboard: typeof gameScoreRepository.getSeasonLeaderboard = (
    ...a
  ) => gameScoreRepository.getSeasonLeaderboard(...a);

  getUserScoresByGame: typeof gameScoreRepository.getUserScoresByGame = (
    ...a
  ) => gameScoreRepository.getUserScoresByGame(...a);

  getUserTopScores: typeof gameScoreRepository.getUserTopScores = (...a) =>
    gameScoreRepository.getUserTopScores(...a);
}

export const gameScoreService = new GameScoreService();
