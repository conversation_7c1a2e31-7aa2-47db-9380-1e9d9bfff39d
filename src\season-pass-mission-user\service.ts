import { ServiceBase } from "../service-base";
import {
  SeasonPassMissionUserEntity,
  seasonPassMissionUserRepository,
} from "./repository";

class SeasonPassMissionUserService extends ServiceBase<
  typeof seasonPassMissionUserRepository,
  SeasonPassMissionUserEntity
> {
  constructor() {
    super(seasonPassMissionUserRepository);
  }

  async getUserMissionsForSeasonPass(
    userId: number,
    seasonPassId: number,
  ): Promise<SeasonPassMissionUserEntity[]> {
    return await this.repository.findByUserAndSeasonPass(userId, seasonPassId);
  }

  updateMissionUser: typeof this.repository.updateMissionUser = (...p) =>
    this.repository.updateMissionUser(...p);
}

export const seasonPassMissionUserService = new SeasonPassMissionUserService();
