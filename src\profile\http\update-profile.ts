import { Middleware } from "@koa/router";
import {
  AppError,
  Auth,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { profileService } from "../service";

export const updateProfile: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  pchujoy.profile.UpdateProfileResponse
> = async ({ request, response, state }) => {
  const body = pchujoy.profile.UpdateProfileRequestSchema.parse(request.body);

  const profile = await profileService.get("userId", state.auth.id);
  if (!profile) {
    throw new AppError({
      code: "ProfileNotFound",
      message: "No se encontró el perfil solicitado",
      statusCode: "NOT_FOUND",
    });
  }
  // Only allow updating the nickname field
  const updatedProfile = await profileService.update(state.auth.id, {
    ...body,
    updatedAt: new Date(),
  });

  response.body =
    pchujoy.profile.UpdateProfileResponseSchema.parse(updatedProfile);
};
