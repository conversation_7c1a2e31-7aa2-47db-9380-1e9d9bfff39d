import { Middleware } from "@koa/router";
import { pchujoy } from "@mainframe-peru/types";
import { seasonPassService } from "../../season-pass/service";
import { seasonPassLevelService } from "../service";

export const listCurrentSeasonPassLevels: Middleware<
  unknown,
  unknown,
  pchujoy.seasonPass.ListSeasonPassLevelsResponse
> = async ({ response }) => {
  const currentSeasonPass = await seasonPassService.getCurrentSeasonPass();
  const entities =
    await seasonPassLevelService.getCurrentSeasonPassLevels(currentSeasonPass);

  response.set("Cache-Control", "s-maxage=1800");
  response.body = entities.map((i) => ({
    id: i.id,
    levelIndex: i.levelIndex,
    name: i.name,
    description: i.description,
    rewards: i.rewards,
    levelExp: i.levelExp, 
    createdAt: i.createdAt,
    updatedAt: i.updatedAt,
  }));
};
