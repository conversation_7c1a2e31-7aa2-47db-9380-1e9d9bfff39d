import { ServiceBase } from "../service-base";
import { SeasonEntity, seasonRepository } from "./repository";

class SeasonService extends ServiceBase<typeof seasonRepository, SeasonEntity> {
  constructor() {
    super(seasonRepository);
  }

  getCurrentSeason: typeof seasonRepository.getCurrentSeason = (...a) =>
    seasonRepository.getCurrentSeason(...a);
}

export const seasonService = new SeasonService();
