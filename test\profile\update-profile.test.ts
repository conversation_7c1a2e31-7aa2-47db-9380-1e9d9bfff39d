import { pchujoy } from "@mainframe-peru/types";
import request from "supertest";
import { accessoryService } from "../../src/accessory";
import { app } from "../../src/api-handler";
import { influencerTable, userTable } from "../../src/core-schema";
import { profileService } from "../../src/profile";
import { sc } from "../../src/services";
import { TestData, userAuthToken } from "../common";

describe("update profile tests", () => {
  beforeEach(async () => {
    // Create dependencies first
    const db = await sc.getDB();
    await db.insert(influencerTable).values(TestData.influencer);
    await db.insert(userTable).values(TestData.user);
    await accessoryService.create(TestData.accessoryHair);
    await accessoryService.create(TestData.accessoryHat);

    // Create a profile before each test
    await profileService.create(TestData.profile);
  });

  test("updates profile successfully", async () => {
    await accessoryService.create(TestData.accessoryFace);

    const requestBody: pchujoy.profile.UpdateProfileRequest = {
      faceId: TestData.accessoryFace.id,
      hatId: null,
      hairId: null,
    };

    const response = await request(app)
      .put("/live/profile")
      .send(requestBody)
      .set("Cookie", `session=${await userAuthToken}`);

    expect(response.status).toBe(200);

    expect(response.body).toEqual({
      userId: TestData.profile.userId,
      faceId: TestData.accessoryFace.id,
      avatarBgColor: TestData.avatarBgColor,
      petBgColor: TestData.petBgColor,
      updatedAt: expect.any(String),
      createdAt: expect.any(String),
    });

    // Verify the profile was actually updated in the database
    const updatedProfile = await profileService.get(
      "userId",
      TestData.profile.userId,
    );
    expect(updatedProfile?.faceId).toBe(TestData.accessoryFace.id);
  });

  test("returns 401 when no authentication token is provided", async () => {
    const requestBody: pchujoy.profile.UpdateProfileRequest = {
      faceId: TestData.accessoryFace.id,
      hatId: null,
      hairId: null,
    };

    const response = await request(app).put("/live/profile").send(requestBody);

    expect(response.status).toBe(401);
  });
});
