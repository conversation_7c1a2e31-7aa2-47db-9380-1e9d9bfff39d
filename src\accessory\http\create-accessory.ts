import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { accessoryService } from "../service";
import { pchujoy } from "@mainframe-peru/types";

export const createAccessory: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  pchujoy.accessory.CreateAccessoryResponse
> = async ({ request, response }) => {
  const body = pchujoy.accessory.CreateAccessoryRequestSchema.parse(
    request.body,
  );
  const accessory = await accessoryService.create(body);

  response.body = {
    id: accessory.id,
    name: accessory.name,
    type: accessory.type,
    value: accessory.value,
    createdAt: accessory.createdAt,
    updatedAt: accessory.updatedAt,
  };
};
