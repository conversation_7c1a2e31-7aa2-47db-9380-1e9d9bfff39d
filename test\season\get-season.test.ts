import { pchujoy } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { seasonService } from "../../src/season";
import { TestData } from "../common";

describe("get season tests", () => {
  test("gets an existing season by id", async () => {
    await seasonService.create(TestData.season);

    const queryParams: pchujoy.season.GetSeasonRequest = {
      id: TestData.season.id,
    };

    const response = await request(app).get("/live/season").query(queryParams);

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      id: TestData.season.id,
      name: TestData.season.name,
      startDate: TestData.season.startDate,
      endDate: TestData.season.endDate,
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    });
  });
});
