import { AppError, Auth } from "@mainframe-peru/common-core";
import { Middleware } from "@koa/router";
import { PgSelectBase } from "drizzle-orm/pg-core";

export type QueryParams = {
  influencerId?: string;
  userId?: number;
};

export function restrictParams<T extends QueryParams>(
  authState: Auth,
  query: T,
): T {
  if (authState.iss === "mainframe:user") {
    query.influencerId = authState.influencerId;
    query.userId = authState.id;
  } else if (authState.iss === "mainframe:admin") {
    query.influencerId = authState.influencerId;
  }
  return query;
}

export function addLimitAndOffsetToQuery<T>(
  filter: {
    offset?: number;
    limit?: number;
  },
  query: T,
): T {
  const q = query as PgSelectBase<"", { a: 1 }, "single">; // placeholder type
  if (filter.limit === undefined) {
    q.limit(100);
  } else if (filter.limit !== -1) {
    q.limit(filter.limit);
  }

  if (filter.offset !== undefined) {
    q.offset(filter.offset);
  }
  return query;
}

// 🚬
// Null-To-Undefined (NTU)
export type NTU<T> =
  T extends Array<unknown>
    ? NTU<T[number]>[]
    : T extends Record<string, unknown>
      ? {
          [P in keyof T]: NTU<T[P]>;
        }
      : T extends null
        ? undefined
        : T;

export function transformNullToUndefined<T>(data: T): NTU<T> {
  if (Array.isArray(data)) {
    return data.map(transformNullToUndefined) as unknown as NTU<T>;
  } else if (data instanceof Date) {
    // Return date objects as-is to avoid conversion issues
    return data as NTU<T>;
  } else if (data && typeof data === "object") {
    const result: Record<string, any> = {}; // eslint-disable-line @typescript-eslint/no-explicit-any
    for (const [key, value] of Object.entries(data)) {
      result[key] =
        value === null ? undefined : transformNullToUndefined(value);
    }
    return result as NTU<T>;
  }
  return data as NTU<T>;
}

type AuthorizedContextState = {
  auth: Auth;
};

export const pchujoyMiddleware: Middleware<
  AuthorizedContextState,
  unknown,
  unknown
> = async ({ state }, next) => {
  if (
    !state.auth.email.endsWith("pchujoy.com") ||
    state.auth.iss !== "mainframe:admin"
  ) {
    throw new AppError({
      code: "Unauthorized",
      message: "User must use a pchujoy.com email",
      logLevel: "INFO",
      statusCode: "FORBIDDEN",
    });
  }

  await next();
};
