import { pchujoy } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { gameService } from "../../src/game";
import { gameScoreService } from "../../src/game-score";
import { gameSeasonService } from "../../src/game-season";
import { seasonService } from "../../src/season";
import { TestData, userAuthToken } from "../common";
import { sc } from "../../src/services";
import { influencerTable, userTable } from "../../src/core-schema";

describe("get user game scores tests", () => {
  test("get user game scores", async () => {
    const db = await sc.getDB();
    await db.insert(influencerTable).values(TestData.influencer);
    await db.insert(userTable).values(TestData.user);
    await gameService.create(TestData.game);
    await seasonService.create(TestData.season);
    await gameSeasonService.create({
      gameId: TestData.game.id,
      seasonId: TestData.season.id,
    });
    for (let i = 0; i < 100; i++) {
      await gameScoreService.create({
        gameId: TestData.game.id,
        seasonId: TestData.season.id,
        userId: TestData.user.id,
        score: i,
      });
    }
    const user2Id = TestData.user.id + 1;
    await db.insert(userTable).values({
      ...TestData.user,
      id: user2Id,
    });
    for (let i = 101; i < 106; i++) {
      await gameScoreService.create({
        gameId: TestData.game.id,
        seasonId: TestData.season.id,
        userId: user2Id,
        score: i,
      });
    }

    const requestBody: pchujoy.gameScore.GetUserScoresRequest = {
      gameId: TestData.game.id,
    };

    const response = await request(app)
      .get("/live/game-score/user/game")
      .query(requestBody)
      .set("Cookie", `session=${await userAuthToken}`);

    const responseBody: pchujoy.gameScore.GetUserScoresResponse = response.body;
    expect(response.status).toBe(200);
    expect(responseBody.length).toEqual(10);
    expect(responseBody[0].rank).toEqual(6);
    expect(responseBody[0].score).toEqual(99);
  });
});
