CREATE SCHEMA "pchujoy";
--> statement-breakpoint
CREATE TYPE "pchujoy"."game_status" AS ENUM('ACTIVE', 'INACTIVE');--> statement-breakpoint
CREATE TABLE "pchujoy"."game_score" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"season_id" integer NOT NULL,
	"game_id" text NOT NULL,
	"score" integer NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "pchujoy"."game_season" (
	"season_id" integer NOT NULL,
	"game_id" text NOT NULL,
	"priority" integer,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "game_season_season_id_game_id_pk" PRIMARY KEY("season_id","game_id")
);
--> statement-breakpoint
CREATE TABLE "pchujoy"."game" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text NOT NULL,
	"image_url" text NOT NULL,
	"source_url" text NOT NULL,
	"status" "pchujoy"."game_status" NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "pchujoy"."season" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"start_date" date NOT NULL,
	"end_date" date NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "pchujoy"."game_score" ADD CONSTRAINT "game_score_season_id_season_id_fk" FOREIGN KEY ("season_id") REFERENCES "pchujoy"."season"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."game_score" ADD CONSTRAINT "game_score_game_id_game_id_fk" FOREIGN KEY ("game_id") REFERENCES "pchujoy"."game"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."game_season" ADD CONSTRAINT "game_season_season_id_season_id_fk" FOREIGN KEY ("season_id") REFERENCES "pchujoy"."season"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pchujoy"."game_season" ADD CONSTRAINT "game_season_game_id_game_id_fk" FOREIGN KEY ("game_id") REFERENCES "pchujoy"."game"("id") ON DELETE no action ON UPDATE no action;