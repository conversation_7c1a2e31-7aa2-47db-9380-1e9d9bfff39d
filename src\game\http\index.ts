import Router from "@koa/router";
import { getClientAuthMiddleware } from "@mainframe-peru/common-core";
import { pchujoyMiddleware } from "../../common";
import { sc } from "../../services";
import { createGame } from "./create-game";
import { deleteGame } from "./delete-game";
import { getGame } from "./get-game";
import { listGame } from "./list-game";
import { updateGame } from "./update-game";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

// Game
router.post("/", authMiddleware, pchujoyMiddleware, createGame);
router.put("/", authMiddleware, pchujoyMiddleware, updateGame);
router.delete("/", authMiddleware, pchujoyMiddleware, deleteGame);

/**
 * Endpoints without authentication
 */
router.get("/", getGame);
router.get("/list", listGame);

export const gameRouter = router;
