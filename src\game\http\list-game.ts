import { Middleware } from "@koa/router";
import { pchujoy } from "@mainframe-peru/types";
import { gameService } from "../service";
import { seasonService } from "../../season";

export const listGame: Middleware<
  unknown,
  unknown,
  pchujoy.game.ListGamesResponse
> = async ({ request, response }) => {
  const body = pchujoy.game.ListGamesRequestSchema.parse(request.query);

  const currentSeason = await seasonService.getCurrentSeason();
  const entities = await gameService.complexList({
    ...body,
    seasonId: body.seasonId || currentSeason?.id,
  });

  response.set("Cache-Control", "s-maxage=1800");
  response.body = entities.map((e) => ({
    id: e.id,
    createdAt: e.createdAt,
    description: e.description,
    imageUrl: e.imageUrl,
    name: e.name,
    sourceUrl: e.sourceUrl,
    status: e.status,
    updatedAt: e.updatedAt,
  }));
};
