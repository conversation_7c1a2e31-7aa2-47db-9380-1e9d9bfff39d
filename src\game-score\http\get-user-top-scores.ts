import { Middleware } from "@koa/router";
import {
  AppError,
  AuthEndUser,
  AuthorizedContextState,
} from "@mainframe-peru/common-core";
import { seasonService } from "../../season";
import { gameScoreService } from "../service";
import { pchujoy } from "@mainframe-peru/types";

export const getUserTopScores: Middleware<
  AuthorizedContextState<AuthEndUser>,
  unknown,
  pchujoy.gameScore.GetUserTopScoresResponse
> = async ({ response, state }) => {
  const season = await seasonService.getCurrentSeason();
  if (!season) {
    throw new AppError({
      code: "NoActiveSeason",
      message: "There is no active season",
      logLevel: "INFO",
      statusCode: "NOT_FOUND",
    });
  }

  response.set("Cache-Control", "s-maxage=1800");
  response.body = await gameScoreService.getUserTopScores({
    seasonId: season.id,
    userId: state.auth.id,
  });
};
