import { InferSelectModel, and, gte, lte, sql } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { seasonTable } from "../schema";
import { sc } from "../services";

export type SeasonEntity = InferSelectModel<typeof seasonTable>;

class SeasonRepository extends RepositoryBase<typeof seasonTable, number> {
  constructor() {
    super(seasonTable);
  }

  async getCurrentSeason(): Promise<SeasonEntity | undefined> {
    const db = await sc.getDB();
    const result = await db
      .select()
      .from(this.table)
      .where(
        and(
          gte(this.table.endDate, sql`now()::date`),
          lte(this.table.startDate, sql`now()::date`),
        ),
      )
      .limit(1);
    return result[0];
  }
}

export const seasonRepository = new SeasonRepository();
