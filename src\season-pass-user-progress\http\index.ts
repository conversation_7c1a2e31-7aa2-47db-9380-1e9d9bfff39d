import Router from "@koa/router";
import { getClientAuthMiddleware } from "@mainframe-peru/common-core";
import { sc } from "../../services";
import { getUserProgress } from "./get-user-progress";
import { claimLevelReward } from "./claim-level-reward";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

router.get("/progress", authMiddleware, getUserProgress);
router.get("/claim", authMiddleware, claimLevelReward);

export { router as seasonPassUserProgressRouter };
