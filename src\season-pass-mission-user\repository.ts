import { and, eq, InferSelectModel } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { seasonPassMissionUserTable } from "../schema";
import { sc } from "../services";

export type SeasonPassMissionUserEntity = InferSelectModel<
  typeof seasonPassMissionUserTable
>;

export type UpdateMissionUserInput = {
  pk: {
    userId: number;
    missionId: number;
  };
  progressCount: number;
  completed: boolean;
};

class SeasonPassMissionUserRepository extends RepositoryBase<
  typeof seasonPassMissionUserTable,
  { userId: number; seasonPassMissionId: number }
> {
  constructor() {
    super(seasonPassMissionUserTable);
  }

  async findByUserAndSeasonPass(
    userId: number,
    seasonPassId: number,
  ): Promise<SeasonPassMissionUserEntity[]> {
    const db = await sc.getDB();
    const result = await db
      .select()
      .from(this.table)
      .where(
        and(
          eq(this.table.userId, userId),
          eq(this.table.seasonPassId, seasonPassId),
        ),
      );
    return result;
  }

  async updateMissionUser(input: UpdateMissionUserInput): Promise<void> {
    const db = await sc.getDB();
    await db
      .update(this.table)
      .set({
        completed: input.completed,
        progressCount: input.progressCount,
      })
      .where(
        and(
          eq(this.table.userId, input.pk.userId),
          eq(this.table.seasonPassMissionId, input.pk.missionId),
        ),
      );
  }
}

export const seasonPassMissionUserRepository =
  new SeasonPassMissionUserRepository();
