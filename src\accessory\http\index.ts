import Router from "@koa/router";
import { getClientAuthMiddleware } from "@mainframe-peru/common-core";
import { pchujoyMiddleware } from "../../common";
import { sc } from "../../services";
import { createAccessory } from "./create-accessory";
import { deleteAccessory } from "./delete-accessory";
import { getAccessory } from "./get-accessory";
import { listAccessory } from "./list-accessory";
import { updateAccessory } from "./update-accessory";

const router = new Router();

/**
 * Endpoints with authentication
 */
const authMiddleware = getClientAuthMiddleware(
  sc.vars.keys.officer.public,
  sc.vars.keys.admin.public,
  sc.vars.keys.user.public,
);

// Accessory
router.post("/", authMiddleware, pchujoyMiddleware, createAccessory);
router.put("/", authMiddleware, pchujoyMiddleware, updateAccessory);
router.delete("/", authMiddleware, pchujoyMiddleware, deleteAccessory);
router.get("/", getAccessory);
router.get("/list", listAccessory);

export const accessoryRouter = router;
