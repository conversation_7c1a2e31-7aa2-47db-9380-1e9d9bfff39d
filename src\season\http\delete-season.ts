import { Middleware } from "@koa/router";
import { Auth, AuthorizedContextState } from "@mainframe-peru/common-core";
import { pchujoy } from "@mainframe-peru/types";
import { seasonService } from "../service";

export const deleteSeason: Middleware<
  AuthorizedContextState<Auth>,
  unknown,
  null
> = async ({ request, response }) => {
  const { id } = pchujoy.season.DeleteSeasonRequestSchema.parse(request.query);
  await seasonService.delete(id);

  response.body = null;
};
