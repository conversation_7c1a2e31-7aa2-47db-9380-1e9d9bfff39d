import { pchujoy } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { gameService } from "../../src/game";
import { TestData, adminAuthToken } from "../common";

describe("delete game tests", () => {
  test("deletes an existing game by id", async () => {
    await gameService.create(TestData.game);

    const queryParams: pchujoy.game.DeleteGameRequest = {
      id: TestData.game.id,
    };

    const response = await request(app)
      .delete("/live/game")
      .query(queryParams)
      .set("Cookie", `session=${await adminAuthToken}`);

    const game = await gameService.get("id", TestData.game.id);
    expect(response.status).toBe(204);
    expect(game).toBeUndefined;
  });
});
