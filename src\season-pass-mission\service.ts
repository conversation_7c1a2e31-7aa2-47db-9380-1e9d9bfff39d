import { ActivityLogEvent } from "@mainframe-peru/types/build/common";
import { webSocketsQueue } from "../common/web-sockets";
import { seasonPassMissionUserService } from "../season-pass-mission-user";
import { seasonPassUserProgressService } from "../season-pass-user-progress";
import { seasonPassService } from "../season-pass/service";
import { ServiceBase } from "../service-base";
import {
  SeasonPassMissionEntity,
  seasonPassMissionRepository,
  ListSeasonPassMissionsInput,
} from "./repository";

export type EvaluateActionInput = {
  action: ActivityLogEvent;
  userId: number;
};

class SeasonPassMissionService extends ServiceBase<
  typeof seasonPassMissionRepository,
  SeasonPassMissionEntity
> {
  constructor() {
    super(seasonPassMissionRepository);
  }

  async list(
    input: ListSeasonPassMissionsInput,
  ): Promise<SeasonPassMissionEntity[]> {
    return this.repository.list(input);
  }

  async evaluateAction(input: EvaluateActionInput): Promise<void> {
    const missions = await this.repository.listCurrentMissions({
      action: input.action,
    });
    const currentSeasonPass = await seasonPassService.getCurrentSeasonPass();

    const userMissions =
      await seasonPassMissionUserService.getUserMissionsForSeasonPass(
        input.userId,
        currentSeasonPass.id,
      );
    const userMissionsById = new Map(
      userMissions.map((m) => [m.seasonPassMissionId, m]),
    );

    for (const m of missions) {
      const um = userMissionsById.get(m.id);
      if (um?.completed) continue;

      const progressCount = (um?.progressCount || 0) + 1;
      const completed = progressCount === m.actionCount;
      if (um) {
        await seasonPassMissionUserService.updateMissionUser({
          pk: {
            missionId: um.seasonPassMissionId,
            userId: um.userId,
          },
          completed,
          progressCount,
        });
      } else {
        await seasonPassMissionUserService.create({
          seasonPassId: currentSeasonPass.id,
          seasonPassMissionId: m.id,
          userId: input.userId,
          progressCount,
          completed,
        });
      }

      if (completed) {
        await seasonPassUserProgressService.addExperience(
          input.userId,
          currentSeasonPass,
          m.experience,
        );
      }

      await webSocketsQueue.sendMessage({
        userId: input.userId,
        message: {
          type: "season-pass-mission",
          name: m.name,
          description: m.description,
          experience: m.experience,
          count: progressCount,
          completed,
        },
      });
    }
  }
}

export const seasonPassMissionService = new SeasonPassMissionService();
