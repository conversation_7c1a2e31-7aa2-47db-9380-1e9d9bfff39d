import { pchujoy } from "@mainframe-peru/types";
import request from "supertest";
import { app } from "../../src/api-handler";
import { gameService } from "../../src/game";
import { TestData, adminAuthToken } from "../common";
import { GameEntity } from "../../src/game/repository";
import { seasonService } from "../../src/season";
import { gameSeasonService } from "../../src/game-season";

describe("create game season tests", () => {
  test("create game season", async () => {
    const games: GameEntity[] = [];
    for (let i = 0; i < 3; i++) {
      const g = await gameService.create({
        ...TestData.game,
        id: i.toString(),
      });
      games.push(g);
    }
    const season = await seasonService.create({
      name: "test",
      endDate: "2024-01-01",
      startDate: "2024-01-01",
    });
    const requestBody: pchujoy.gameSeason.CreateGameSeasonRequest = games.map(
      (g) => ({
        gameId: g.id,
        seasonId: season.id,
      }),
    );

    const response = await request(app)
      .post("/live/game-season")
      .send(requestBody)
      .set("Cookie", `session=${await adminAuthToken}`);

    const savedSeasons = await gameSeasonService.list({
      seasonId: season.id,
    });
    expect(response.status).toBe(200);
    expect(response.body.amount).toBe(3);
    expect(savedSeasons.length).toEqual(3);
  });
});
