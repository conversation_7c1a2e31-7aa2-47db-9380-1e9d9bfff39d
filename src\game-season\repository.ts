import { InferSelectModel, and, eq } from "drizzle-orm";
import { RepositoryBase } from "../repository-base";
import { gameSeasonTable } from "../schema";
import { sc } from "../services";

export type GameSeasonEntity = InferSelectModel<typeof gameSeasonTable>;

class GameSeasonRepository extends RepositoryBase<
  typeof gameSeasonTable,
  number
> {
  constructor() {
    super(gameSeasonTable);
  }

  async deleteBySeasonAndGame(seasonId: number, gameId: string): Promise<void> {
    const db = await sc.getDB();
    await db
      .delete(this.table)
      .where(
        and(eq(this.table.seasonId, seasonId), eq(this.table.gameId, gameId)),
      );
  }
}

export const gameSeasonRepository = new GameSeasonRepository();
