import { AppError } from "@mainframe-peru/common-core";
import { seasonPassUserProgressRepository, seasonPassUserProgressService } from "../season-pass-user-progress";
import { SeasonPassEntity } from "../season-pass/repository";
import { seasonPassService } from "../season-pass/service";
import { ServiceBase } from "../service-base";
import { SeasonPassLevelEntity, seasonPassLevelRepository } from "./repository";
import { inventoryRepository } from "../inventory";
import { coreBackendQueue } from "../common/core-backend";

class SeasonPassLevelService extends ServiceBase<
  typeof seasonPassLevelRepository,
  SeasonPassLevelEntity
> {
  constructor() {
    super(seasonPassLevelRepository);
  }

  async getCurrentSeasonPassLevels(
    currentSeasonPass: SeasonPassEntity,
  ): Promise<SeasonPassLevelEntity[]> {
    return this.repository.getBySeasonPassId(currentSeasonPass.id);
  }

  getFirstLevel: typeof this.repository.getFirstLevel = (...p) =>
    this.repository.getFirstLevel(...p);

  async claimLevelReward(userId: number, levelId: number): Promise<void> {
    const level = await this.get("id", levelId);

    if(!level) {
      throw new AppError({
        code: "LevelNotFound",
        message: "El nivel no existe",
        statusCode: "NOT_FOUND",
      });
    }

    const currentSeasonPass = await seasonPassService.getCurrentSeasonPass();
    
    const progress = await seasonPassUserProgressService.getUserProgress(
      userId,
      currentSeasonPass.id,
    );
    
    if (!progress || progress.currentLevel < level.levelIndex) {
      throw new AppError({
        code: "UserProgressInsufficient",
        message:
          "El usuario no ha alcanzado el nivel para reclamar la recompensa",
        statusCode: "BAD_REQUEST",
      });
    }

    if(progress.levelsClaimed.includes(level.id)) {
      throw new AppError({
        code: "RewardAlreadyClaimed",
        message:
          "El usuario ya reclamó esta recompensa",
        statusCode: "BAD_REQUEST",
      });
    }

    for(const reward of level.rewards) {
      if(reward.type === "ACCESSORY") {     
        const item = await inventoryRepository.findAccessoryInInventory(userId, reward.id);
        if(item) {
          throw new AppError({
            code: "ItemAlreadyOwned",
            message:
              "El usuario ya posee el item",
            statusCode: "NOT_FOUND",
          });
        } else {
          await inventoryRepository.create({
            accessoryId: reward.id,
            userId,
          });
        } 
      } else if(reward.type === "EVENT") {
        await coreBackendQueue.sendAddUserEventParticipationMessage({
          userId,
          eventId: reward.id,
        });
      }
    }

    await seasonPassUserProgressRepository.updateProgress(userId, currentSeasonPass.id, {
      levelsClaimed: [...progress.levelsClaimed, level.id],
    });
  }
}

export const seasonPassLevelService = new SeasonPassLevelService();
